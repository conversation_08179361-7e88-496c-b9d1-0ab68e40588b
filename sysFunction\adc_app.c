#include "my_config.h"

extern uint16_t adc_value[1];
extern sampling_state_t sampling_state;
extern config_params_t system_config;

uint32_t last_tick_sampling;
static uint32_t next_sample_time = 0;

static uint32_t adc_sample_count = 0;
static uint16_t last_adc_value = ADC_INVALID_VALUE;

void sampling_task(void);

uint8_t adc_is_data_ready(void)
{
    if (adc_value[0] > (uint16_t)ADC_MAX_VALUE) {
        REPORT_WARNING(ERR_ADC_DATA_INVALID, "adc_app", "ADC value exceeds maximum range");
        return 0;
    }

    if (adc_value[0] == 0 || adc_value[0] == 0xFFFF) {
        return 0;
    }

    if (adc_value[0] == last_adc_value) {
        return 0;
    }

    return 1;
}

static inline float calculate_voltage(uint16_t adc_raw)
{
    if (adc_raw > (uint16_t)ADC_MAX_VALUE) {
        REPORT_WARNING(ERR_ADC_DATA_INVALID, "adc_app", "ADC raw value out of range");
        return 0.0f;
    }

    if (system_config.ratio_ch0 <= MIN_CONFIG_RATIO || system_config.ratio_ch0 > MAX_CONFIG_RATIO) {
        REPORT_WARNING(ERR_CONFIG_INVALID, "adc_app", "Invalid ratio configuration");
        return 0.0f;
    }

    float voltage_raw = (adc_raw * ADC_REFERENCE_VOLTAGE) / ADC_MAX_VALUE;
    return voltage_raw * system_config.ratio_ch0;
}

void adc_task(void)
{
    sampling_task();
}

void sampling_task(void)
{
    if (!sampling_state.is_sampling) {
        return;
    }

    uint32_t current_time = get_system_ms();

    if (current_time >= next_sample_time) {
        next_sample_time = current_time + (sampling_state.sample_period * SAMPLE_PERIOD_TO_MS);
        last_tick_sampling = current_time;

        if (!adc_is_data_ready()) {
            return;
        }

        last_adc_value = adc_value[0];

        adc_sample_count++;

        float voltage = calculate_voltage(adc_value[0]);

        uint8_t over_limit = (voltage > system_config.limit_ch0) ? 1 : 0;

        print_sample_data(voltage, over_limit);

        oled_update_voltage(voltage, get_unix_timestamp());

        led2_overlimit_set(over_limit);
    }
}

uint32_t adc_get_sample_count(void)
{
    return adc_sample_count;
}

void adc_reset_stats(void)
{
    adc_sample_count = 0;
    last_adc_value = ADC_INVALID_VALUE;
}

float adc_get_current_voltage(void)
{
    if (!adc_is_data_ready()) {
        return 0.0f;
    }

    return calculate_voltage(adc_value[0]);
}
