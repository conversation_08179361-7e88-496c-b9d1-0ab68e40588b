/* 系统架构部分样式 */
.architecture-section {
    background: var(--bg-primary);
}

.architecture-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.architecture-diagram {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.layer {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.layer:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-lg);
}

.layer h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.layer-components {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.component {
    background: var(--bg-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.component:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: scale(1.05);
}

.application-layer {
    border-left-color: #ef4444;
}

.function-layer {
    border-left-color: var(--primary-color);
}

.driver-layer {
    border-left-color: var(--accent-color);
}

.hal-layer {
    border-left-color: var(--success-color);
}

.architecture-description {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.architecture-description h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.feature {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.feature i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-top: var(--spacing-xs);
    flex-shrink: 0;
}

.feature h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.feature p {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 功能模块部分样式 */
.modules-section {
    background: var(--bg-secondary);
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.module-card {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.module-card:hover::before {
    transform: scaleX(1);
}

.module-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.module-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    transition: all 0.3s ease;
}

.module-card:hover .module-icon {
    transform: scale(1.1) rotate(5deg);
}

.module-icon i {
    font-size: 1.5rem;
    color: var(--text-white);
}

.module-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.module-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.module-stats {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.stat {
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

/* 技术文档部分样式 */
.docs-section {
    background: var(--bg-primary);
}

.docs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.doc-card {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.doc-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 20px 20px 0;
    border-color: transparent var(--primary-color) transparent transparent;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.doc-card:hover::after {
    opacity: 1;
}

.doc-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.doc-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--accent-color), #f59e0b);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    transition: all 0.3s ease;
}

.doc-card:hover .doc-icon {
    transform: scale(1.1);
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
}

.doc-icon i {
    font-size: 1.5rem;
    color: var(--text-white);
}

.doc-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.doc-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.doc-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.doc-pages,
.doc-type {
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.doc-type {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

/* 系统演示部分样式 */
.demo-section {
    background: var(--bg-secondary);
}

.demo-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.demo-controls {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.demo-controls h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.control-group {
    margin-bottom: var(--spacing-lg);
}

.control-group label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.control-group input[type="range"] {
    width: 100%;
    margin: var(--spacing-sm) 0;
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: var(--bg-secondary);
    border-radius: 3px;
    outline: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.demo-display {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.virtual-oled {
    background: #000;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 2px solid #333;
}

.oled-screen {
    background: #001122;
    padding: var(--spacing-md);
    border-radius: 4px;
    font-family: var(--font-mono);
    color: #00ff00;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.oled-line {
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 2px 0;
}

.led-panel {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    background: var(--bg-card);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.led {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #333;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.led::after {
    content: attr(data-label);
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.7rem;
    color: var(--text-secondary);
    white-space: nowrap;
    margin-top: var(--spacing-xs);
}

.led.on {
    background: radial-gradient(circle, #ff4444, #cc0000);
    box-shadow: 0 0 20px rgba(255, 68, 68, 0.6);
}

.led.blink {
    animation: ledBlink 1s infinite;
}

.demo-chart {
    grid-column: 1 / -1;
    background: var(--bg-card);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

/* 页脚样式 */
.footer {
    background: var(--bg-dark);
    color: var(--text-white);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h3 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-white);
}

.footer-section p,
.footer-section li {
    color: #94a3b8;
    line-height: 1.6;
    margin-bottom: var(--spacing-xs);
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    padding: var(--spacing-xs) 0;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: var(--spacing-lg);
    text-align: center;
    color: #6b7280;
}

/* 动画效果 */
@keyframes ledBlink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .architecture-content {
        grid-template-columns: 1fr;
    }

    .architecture-description {
        position: static;
    }

    .demo-content {
        grid-template-columns: 1fr;
    }

    .modules-grid {
        grid-template-columns: 1fr;
    }

    .docs-grid {
        grid-template-columns: 1fr;
    }

    .led-panel {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .layer-components {
        flex-direction: column;
    }

    .component {
        text-align: center;
    }

    .module-stats {
        justify-content: center;
    }

    .doc-meta {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }

    .led-panel {
        grid-template-columns: 1fr;
    }

    .control-group {
        text-align: center;
    }
}

/* 面包屑导航样式 */
.breadcrumb-container {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-sm) 0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.9rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.breadcrumb-item:hover {
    background: var(--bg-primary);
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 600;
}

.breadcrumb-separator {
    color: var(--text-light);
    font-size: 0.8rem;
}

/* 搜索功能样式 */
.search-container {
    position: relative;
    margin-left: auto;
}

.search-box {
    display: flex;
    align-items: center;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-input {
    border: none;
    outline: none;
    padding: var(--spacing-sm) var(--spacing-md);
    background: transparent;
    color: var(--text-primary);
    width: 250px;
    font-size: 0.9rem;
}

.search-input::placeholder {
    color: var(--text-light);
}

.search-button {
    background: var(--primary-color);
    border: none;
    color: var(--text-white);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-button:hover {
    background: var(--primary-dark);
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-result-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-result-item:hover {
    background: var(--bg-secondary);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.search-result-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
}

.search-result-type {
    font-size: 0.8rem;
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    display: inline-block;
}

.search-no-results {
    padding: var(--spacing-lg);
    text-align: center;
    color: var(--text-secondary);
}

/* 高亮样式 */
.highlight {
    animation: highlight 2s ease-out;
}

@keyframes highlight {
    0% {
        background: rgba(37, 99, 235, 0.3);
        transform: scale(1.02);
    }
    100% {
        background: transparent;
        transform: scale(1);
    }
}

/* 技术规格展示样式 */
.specs-section {
    background: var(--bg-secondary);
}

.specs-content {
    margin-top: var(--spacing-xl);
}

.specs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.spec-category {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.spec-category:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.spec-category h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 600;
}

.spec-category h3 i {
    font-size: 1.5rem;
    color: var(--accent-color);
}

.spec-table {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.spec-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.spec-row:last-child {
    border-bottom: none;
}

.spec-label {
    color: var(--text-secondary);
    font-weight: 500;
    flex: 1;
}

.spec-value {
    color: var(--text-primary);
    font-weight: 600;
    font-family: var(--font-mono);
    background: rgba(37, 99, 235, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    font-size: 0.9rem;
}

/* 性能指标展示样式 */
.performance-section {
    background: var(--bg-primary);
}

.performance-content {
    margin-top: var(--spacing-xl);
}

.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.performance-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    text-align: center;
    transition: all 0.3s ease;
}

.performance-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.performance-chart {
    margin-bottom: var(--spacing-lg);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.performance-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    font-size: 1.25rem;
    font-weight: 600;
}

.performance-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    flex: 1;
    text-align: left;
}

.stat-value {
    color: var(--text-primary);
    font-weight: 600;
    font-family: var(--font-mono);
    margin-right: var(--spacing-sm);
}

.stat-percent {
    color: var(--success-color);
    font-weight: 600;
    font-size: 0.9rem;
    background: rgba(16, 185, 129, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
}

/* 项目亮点展示样式 */
.highlights-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-white);
    position: relative;
    overflow: hidden;
}

.highlights-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.highlights-section .section-title {
    color: var(--text-white);
    position: relative;
    z-index: 1;
}

.highlights-section .section-title::after {
    background: linear-gradient(90deg, var(--accent-color), #ffffff);
}

.highlights-content {
    position: relative;
    z-index: 1;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.highlight-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    text-align: center;
}

.highlight-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.highlight-card.featured {
    background: rgba(245, 158, 11, 0.2);
    border-color: var(--accent-color);
}

.highlight-card.featured:hover {
    background: rgba(245, 158, 11, 0.3);
}

.highlight-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    transition: all 0.3s ease;
}

.highlight-card:hover .highlight-icon {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

.highlight-icon i {
    font-size: 2rem;
    color: var(--accent-color);
}

.highlight-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-white);
}

.highlight-card p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
}

.highlight-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.stat-badge {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 技术创新总结样式 */
.innovation-summary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: var(--spacing-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
}

.innovation-summary h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xl);
    color: var(--text-white);
}

.innovation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-xl);
}

.innovation-item {
    text-align: center;
}

.innovation-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--accent-color);
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.innovation-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-white);
    opacity: 0.9;
}

/* 架构图和流程图样式 */
.architecture-visual,
.dataflow-visual,
.modules-visual {
    margin: var(--spacing-2xl) 0;
    text-align: center;
}

.architecture-visual h3,
.dataflow-visual h3,
.modules-visual h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.architecture-image,
.dataflow-image,
.modules-image {
    width: 100%;
    max-width: 900px;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    background: var(--bg-card);
    padding: var(--spacing-md);
}

.architecture-image:hover,
.dataflow-image:hover,
.modules-image:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-xl);
}

/* 图片懒加载优化 */
.architecture-image,
.dataflow-image,
.modules-image {
    loading: lazy;
    opacity: 0;
    animation: fadeInImage 0.6s ease-out forwards;
}

@keyframes fadeInImage {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式图片优化 */
@media (max-width: 768px) {
    .architecture-image,
    .dataflow-image,
    .modules-image {
        padding: var(--spacing-sm);
        max-width: 100%;
    }

    .architecture-visual h3,
    .dataflow-visual h3,
    .modules-visual h3 {
        font-size: 1.25rem;
    }
}

/* 面包屑导航样式 */
.breadcrumb-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-sm) 0;
    position: sticky;
    top: 70px;
    z-index: 999;
    transition: all 0.3s ease;
}

.breadcrumb {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    font-size: 0.9rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin: 0 var(--spacing-sm);
    color: var(--text-light);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.breadcrumb-item a:hover {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-dark);
}

.breadcrumb-item.active span {
    color: var(--text-secondary);
    font-weight: 500;
}

/* 加载状态指示器样式 */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    display: none;
    align-items: center;
    gap: var(--spacing-md);
    z-index: 10000;
}

.loading-indicator.show {
    display: flex;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-primary);
    font-weight: 500;
}

/* 代码查看器样式 */
.code-viewer-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10001;
    display: none;
}

.code-viewer-modal.show {
    display: block;
}

.code-viewer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-lg);
}

.code-viewer-content {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    width: 1000px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.code-viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.code-viewer-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.code-viewer-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.code-viewer-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.code-viewer-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.code-viewer-body {
    flex: 1;
    overflow: auto;
    background: #1e293b;
}

.code-viewer-body pre {
    margin: 0;
    padding: var(--spacing-lg);
    background: transparent;
    color: #e2e8f0;
    font-family: var(--font-mono);
    font-size: 14px;
    line-height: 1.6;
}

.code-viewer-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.code-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.code-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* 文档预览样式 */
.doc-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10001;
    display: none;
}

.doc-preview-modal.show {
    display: block;
}

.doc-preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-lg);
}

.doc-preview-content {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    width: 1200px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.doc-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.doc-preview-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.doc-preview-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.doc-preview-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.doc-preview-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.doc-preview-body {
    flex: 1;
    overflow: auto;
    padding: var(--spacing-xl);
    background: var(--bg-card);
}

.doc-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.doc-loading i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* 文档内容样式 */
.doc-preview-body h1,
.doc-preview-body h2,
.doc-preview-body h3,
.doc-preview-body h4,
.doc-preview-body h5,
.doc-preview-body h6 {
    color: var(--text-primary);
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-md);
}

.doc-preview-body h1 {
    font-size: 2rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: var(--spacing-sm);
}

.doc-preview-body h2 {
    font-size: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-xs);
}

.doc-preview-body p {
    line-height: 1.7;
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

.doc-preview-body ul,
.doc-preview-body ol {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-xl);
}

.doc-preview-body li {
    margin-bottom: var(--spacing-xs);
    color: var(--text-secondary);
}

.doc-preview-body code {
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 4px;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--primary-color);
}

.doc-preview-body pre {
    background: #1e293b;
    color: #e2e8f0;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    overflow-x: auto;
    margin-bottom: var(--spacing-md);
}

.doc-preview-body blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    background: var(--bg-secondary);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.doc-preview-body table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-lg);
}

.doc-preview-body th,
.doc-preview-body td {
    border: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
}

.doc-preview-body th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .breadcrumb-nav {
        top: 60px;
    }

    .code-viewer-content,
    .doc-preview-content {
        max-width: 95vw;
        max-height: 95vh;
        margin: var(--spacing-sm);
    }

    .code-viewer-header,
    .doc-preview-header {
        padding: var(--spacing-md);
    }

    .code-viewer-body pre {
        padding: var(--spacing-md);
        font-size: 12px;
    }

    .doc-preview-body {
        padding: var(--spacing-md);
    }
}

/* 增强搜索结果样式 */
.search-result-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.search-result-item:hover,
.search-result-item.active {
    background: var(--bg-secondary);
    border-left: 4px solid var(--primary-color);
    padding-left: calc(var(--spacing-md) - 4px);
}

.search-result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-xs);
}

.search-result-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    line-height: 1.4;
}

.search-result-title mark {
    background: rgba(37, 99, 235, 0.2);
    color: var(--primary-color);
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 700;
}

.search-result-score {
    background: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
}

.search-result-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: var(--spacing-sm);
}

.search-result-description mark {
    background: rgba(245, 158, 11, 0.2);
    color: var(--accent-color);
    padding: 1px 3px;
    border-radius: 2px;
}

.search-result-meta {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.search-result-type {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.search-result-match {
    color: var(--text-light);
    font-size: 0.75rem;
    font-style: italic;
}

.search-no-results {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.search-no-results i {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-light);
}

.search-no-results span {
    display: block;
    font-size: 1rem;
    margin-bottom: var(--spacing-sm);
}

.search-suggestions {
    margin-top: var(--spacing-md);
}

.search-suggestions small {
    color: var(--text-light);
    font-size: 0.8rem;
}

/* 搜索框增强样式 */
.search-container .search-box input {
    padding-right: 100px;
}

.search-container .search-box::after {
    content: 'Ctrl+K';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--bg-secondary);
    color: var(--text-light);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-family: var(--font-mono);
    pointer-events: none;
}

/* 键盘导航提示 */
.search-results::before {
    content: '↑↓ 导航 • Enter 选择 • Esc 关闭';
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    color: var(--text-light);
    font-size: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    font-family: var(--font-mono);
}

/* 代码查看器按钮样式 */
.code-view-btn {
    margin-top: var(--spacing-md);
    width: 100%;
    justify-content: center;
    gap: var(--spacing-sm);
}

.doc-preview-btn {
    margin-top: var(--spacing-md);
    width: 100%;
    justify-content: center;
    gap: var(--spacing-sm);
}