# 系统单元功能分析设计

## 概述

本文档深入分析GD32F470VET6数据采集与存储系统的9个核心功能模块，包括各模块的功能描述、接口定义、数据结构、关键算法和模块间交互关系。系统采用模块化设计，每个模块职责单一，接口清晰，便于维护和扩展。

## 模块关系图

```mermaid
graph TB
    subgraph "核心调度"
        A[scheduler<br/>任务调度器]
    end
    
    subgraph "数据处理"
        B[adc_app<br/>ADC采样]
        C[sd_app<br/>存储管理]
    end
    
    subgraph "用户交互"
        D[oled_app<br/>显示控制]
        E[btn_app<br/>按键处理]
        F[led_app<br/>LED指示]
    end
    
    subgraph "系统服务"
        G[usart_app<br/>串口通信]
        H[rtc_app<br/>时钟管理]
        I[error_handler<br/>错误处理]
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G
    A --> H
    
    B --> C
    B --> D
    B --> F
    B --> I
    
    C --> I
    C --> H
    
    E --> B
    E --> G
    
    G --> C
    G --> H
    G --> I
    
    H --> C
    
    I --> G
```

## 1. 任务调度器模块 (scheduler)

### 功能描述
任务调度器是系统的核心控制模块，采用时间片轮询调度算法，负责协调6个功能任务的执行，确保系统的实时性和稳定性。

### 接口定义
```c
// 调度器初始化
void scheduler_init(void);

// 调度器运行（主循环调用）
void scheduler_run(void);

// 获取调度器运行次数统计
uint32_t scheduler_get_run_count(void);

// 重置性能统计
void scheduler_reset_stats(void);
```

### 数据结构
```c
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;          // 任务执行周期（毫秒）
    uint32_t last_run;         // 上次运行时间（毫秒）
    uint32_t next_run;         // 预计算的下次运行时间（毫秒）
} task_t;

// 静态任务数组
static task_t scheduler_task[] = {
    {adc_task,  100,  0,  100},   // ADC任务，100ms周期
    {led_task,  50,   0,  50},    // LED任务，50ms周期
    {oled_task, 100,  0,  100},   // OLED任务，100ms周期
    {btn_task,  5,    0,  5},     // 按键任务，5ms周期（高优先级）
    {uart_task, 5,    0,  5},     // 串口任务，5ms周期（高优先级）
    {rtc_task,  500,  0,  500}    // RTC任务，500ms周期
};
```

### 关键算法
**时间片轮询调度算法**:
1. 获取当前系统时间
2. 遍历所有任务，检查是否到达执行时间
3. 执行到期任务，更新下次执行时间
4. 统计调度器运行次数

**性能优化特点**:
- 预计算下次运行时间，避免重复计算
- 单次时间获取，减少系统调用开销
- 静态任务数组，避免动态内存分配

### 模块交互
- **输入**: 系统时钟tick（get_system_ms()）
- **输出**: 调用各功能模块的任务函数
- **依赖**: systick时钟、perf_counter性能计数器

## 2. ADC采样模块 (adc_app)

### 功能描述
ADC采样模块负责高精度电压采样、数据处理和超限检测，支持可配置的采样周期和变比系数，提供实时电压监测功能。

### 接口定义
```c
// ADC任务主函数
void adc_task(void);

// 检查ADC数据是否准备就绪
uint8_t adc_is_data_ready(void);

// 获取当前电压值
float adc_get_current_voltage(void);

// 获取采样次数统计
uint32_t adc_get_sample_count(void);

// 重置ADC性能统计
void adc_reset_stats(void);
```

### 数据结构
```c
// ADC原始数据缓冲区
extern uint16_t adc_value[1];

// 采样状态结构
typedef struct {
    uint8_t is_sampling;      // 采样状态：0=停止，1=运行
    uint32_t sample_period;   // 采样周期，单位秒
    uint32_t last_sample_time; // 上次采样时间（系统tick）
    uint8_t hide_mode;        // 数据隐藏模式：0=正常显示，1=隐藏显示
} sampling_state_t;

// 配置参数结构
typedef struct {
    float ratio_ch0;        // ADC通道0变比系数
    float limit_ch0;        // ADC通道0阈值限制
    uint32_t sample_period; // 采样周期，单位秒
} config_params_t;
```

### 关键算法
**电压计算算法**:
```c
static inline float calculate_voltage(uint16_t adc_raw)
{
    // 安全检查：验证ADC原始值范围
    if (adc_raw > (uint16_t)ADC_MAX_VALUE) {
        return 0.0f;
    }
    
    // 电压计算：ADC值 → 电压值 → 变比换算
    float voltage_raw = (adc_raw * ADC_REFERENCE_VOLTAGE) / ADC_MAX_VALUE;
    return voltage_raw * system_config.ratio_ch0;
}
```

**数据有效性检查**:
1. ADC值范围检查（0-4095）
2. 数据变化检测（避免读取过时数据）
3. 配置参数有效性验证

### 模块交互
- **输入**: ADC硬件采样数据、系统配置参数
- **输出**: 电压数据、超限状态、采样统计
- **通知**: OLED显示更新、LED状态指示、数据存储

## 3. 存储管理模块 (sd_app)

### 功能描述
存储管理模块实现双重存储机制，包括SD卡主存储和Flash备份存储，支持文件系统操作、数据加密、配置管理等功能。

### 接口定义
```c
// SD卡文件系统初始化
void sd_fatfs_init(void);

// 存储系统初始化
void storage_init(void);

// 数据存储函数
void store_sample_data(float voltage, uint8_t over_limit);
void store_overlimit_data(float voltage);
void store_hidedata(float voltage, uint8_t over_limit);

// 日志操作
void log_operation(const char* operation);

// Flash配置管理
void save_config_to_file(void);
void load_flash_log0(void);
void save_flash_log0(void);

// 文件系统诊断
void show_current_log_status(void);
void verify_log_files_integrity(void);
```

### 数据结构
```c
// 存储状态结构
typedef struct {
    uint32_t log_id;           // 当前日志ID
    uint8_t sd_card_ready;     // SD卡就绪状态
    uint8_t flash_backup_ready; // Flash备份就绪状态
} storage_state_t;

// 文件句柄
extern FIL current_log_file;
extern FIL current_sample_file;
extern FIL current_overlimit_file;
extern FIL current_hidedata_file;

// 文件状态标志
extern uint8_t log_file_open;
extern uint8_t sample_file_open;
extern uint8_t overlimit_file_open;
extern uint8_t hidedata_file_open;
```

### 关键算法
**双重存储策略**:
1. **主存储**: SD卡文件系统（大容量、可移动）
2. **备份存储**: 内部Flash（可靠性高、断电保存）

**目录结构管理**:
```
SD卡根目录/
├── sample/     # 采样数据文件
├── overLimit/  # 超限数据文件
├── log/        # 系统日志文件
└── hideData/   # 加密数据文件
```

**数据加密算法**:
```c
uint32_t voltage_to_encrypted_hex(float voltage)
{
    // 16.16定点数格式转换
    uint32_t fixed_point = (uint32_t)(voltage * VOLTAGE_FRAC_MULTIPLIER);
    
    // 简单异或加密
    uint32_t encrypted = fixed_point ^ 0xA5A5A5A5;
    
    return encrypted;
}
```

### 模块交互
- **输入**: 采样数据、配置参数、系统状态
- **输出**: 存储状态、文件操作结果
- **依赖**: FatFS文件系统、RTC时钟、错误处理

## 4. 显示控制模块 (oled_app)

### 功能描述
OLED显示控制模块负责0.91寸OLED屏幕的显示管理，支持实时电压显示、时间显示、系统状态显示等功能。

### 接口定义
```c
// OLED任务主函数
void oled_task(void);

// 格式化输出函数
int oled_printf(uint8_t x, uint8_t y, const char *format, ...);

// 电压显示同步函数
void oled_update_voltage(float voltage, uint32_t timestamp);
```

### 数据结构
```c
// OLED显示同步变量
static float last_displayed_voltage = 0.0f;  // 上次显示的电压值
static uint32_t last_displayed_time = 0;     // 上次显示的时间戳
static uint8_t voltage_updated = 0;          // 电压更新标志
```

### 关键算法
**显示状态管理**:
1. **空闲状态**: 显示"system idle"
2. **采样状态**: 显示时间和电压值

**显示优化策略**:
- 状态变化时才更新显示，避免频繁刷新
- 电压更新标志机制，确保数据同步
- 格式化显示，整数和小数部分分离

### 模块交互
- **输入**: 采样状态、电压数据、时间戳
- **输出**: OLED屏幕显示内容
- **依赖**: OLED硬件驱动、RTC时间转换

## 5. 按键处理模块 (btn_app)

### 功能描述
按键处理模块基于ebtn库实现7个按键的输入处理，支持防抖、多种按键事件检测，提供用户交互功能。

### 接口定义
```c
// 按键应用初始化
void app_btn_init(void);

// 按键任务处理
void btn_task(void);

// BSP层按键初始化
void bsp_btn_init(void);
```

### 数据结构
```c
// 按键枚举定义
typedef enum {
    USER_BUTTON_0 = 0,  // KEY1
    USER_BUTTON_1,      // KEY2
    USER_BUTTON_2,      // KEY3
    USER_BUTTON_3,      // KEY4
    USER_BUTTON_4,      // KEY5
    USER_BUTTON_5,      // KEY6
    USER_BUTTON_6,      // KEYW
    USER_BUTTON_MAX,
} user_button_t;

// 按键参数配置
static const ebtn_btn_param_t defaul_ebtn_param = 
    EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

// 按键数组
static ebtn_btn_t btns[] = {
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param),
    // ... 其他按键
};
```

### 关键算法
**按键状态读取**:
```c
uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
    switch (btn->key_id) {
        case USER_BUTTON_0: return !KEY1_READ;
        case USER_BUTTON_1: return !KEY2_READ;
        // ... 其他按键
        default: return 0;
    }
}
```

**按键事件处理**:
- KEY1: 采样启动/停止控制
- KEY2-KEY6: 功能扩展按键
- KEYW: 特殊功能按键

### 模块交互
- **输入**: GPIO按键状态
- **输出**: 按键事件、功能控制命令
- **依赖**: ebtn按键库、GPIO硬件

## 6. 串口通信模块 (usart_app)

### 功能描述
串口通信模块提供调试输出、命令解析、配置管理等功能，支持115200波特率通信，实现人机交互和系统监控。

### 接口定义
```c
// 串口任务处理
void uart_task(void);

// 格式化输出函数
void my_printf(usart_periph_enum usart_periph, const char *format, ...);

// 命令处理函数
void sampling_start_handler(void);
void sampling_stop_handler(void);
void config_set_handler(char* param, char* value);

// 系统状态查询
void system_comprehensive_status(void);
void show_current_log_status(void);
```

### 数据结构
```c
// 串口接收缓冲区
extern uint8_t rxbuffer[512];

// 命令解析缓冲区
static char line_buffer[LINE_BUFFER_SIZE];
static uint16_t line_index = 0;
```

### 关键算法
**命令解析流程**:
1. 串口数据接收（DMA + 空闲中断）
2. 命令行解析（按行处理）
3. 参数提取和验证
4. 功能函数调用
5. 结果反馈输出

**支持的命令类型**:
- 采样控制: start, stop
- 参数配置: set ratio, set limit, set period
- 状态查询: status, log_status
- 系统管理: reset, device_id

### 模块交互
- **输入**: 串口接收数据、系统状态
- **输出**: 调试信息、状态报告、配置确认
- **依赖**: USART硬件、DMA传输

## 7. 时钟管理模块 (rtc_app)

### 功能描述
RTC时钟管理模块提供实时时钟功能、时间戳生成、上电次数管理等服务，支持断电保存和时区转换。

### 接口定义
```c
// RTC任务处理
void rtc_task(void);

// RTC初始化和配置
int bsp_rtc_init(void);
uint8_t bsp_rtc_need_init(void);

// 时间戳相关
uint32_t get_unix_timestamp(void);
local_time_t timestamp_to_local_time(uint32_t timestamp);

// 上电次数管理
uint32_t bsp_rtc_get_power_count(void);
void bsp_rtc_increment_power_count(void);
void bsp_rtc_reset_power_count(void);
void bsp_rtc_delayed_increment_power_count(void);

// 数据格式转换
uint8_t bcd_to_dec(uint8_t bcd);
uint8_t dec_to_bcd(uint8_t dec);
```

### 数据结构
```c
// 本地时间结构
typedef struct {
    uint16_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
} local_time_t;

// RTC备份寄存器分配
#define RTC_BKP0            // RTC初始化标识
#define RTC_POWER_COUNT_BKP // 上电次数存储
#define RTC_RESET_FLAG_BKP  // 重置标志存储
```

### 关键算法
**Unix时间戳转换**:
```c
local_time_t timestamp_to_local_time(uint32_t timestamp)
{
    // 时区偏移处理（UTC+8）
    uint32_t local_timestamp = timestamp + TIMEZONE_OFFSET_SECONDS;
    
    // 天数和秒数分离
    uint32_t days_since_epoch = local_timestamp / 86400;
    uint32_t seconds_in_day = local_timestamp % 86400;
    
    // 年月日计算（考虑闰年）
    // 时分秒计算
    // ...
}
```

**上电次数管理策略**:
- 系统启动时检查RTC状态
- 延迟递增机制，避免异常重启计数
- 重置标志管理，区分正常启动和重置启动

### 模块交互
- **输入**: RTC硬件时钟、系统启动状态
- **输出**: 时间戳、本地时间、上电次数
- **依赖**: RTC硬件、备份域寄存器

## 8. LED指示模块 (led_app)

### 功能描述
LED指示模块管理6个LED的状态显示，提供采样状态指示、超限报警、系统状态显示等功能。

### 接口定义
```c
// LED任务处理
void led_task(void);

// LED状态控制
void led2_overlimit_set(uint8_t state);

// 性能统计
uint32_t led_get_gpio_operations(void);
void led_reset_stats(void);

// BSP层LED初始化
void bsp_led_init(void);
```

### 数据结构
```c
// LED状态数组
extern uint8_t led_state_array[6];

// LED索引定义
#define LED1_INDEX  0  // 采样状态指示
#define LED2_INDEX  1  // 超限报警指示
// LED3-LED6: 系统状态指示

// 性能统计
static uint32_t gpio_operation_count = 0;
```

### 关键算法
**LED1采样指示逻辑**:
```c
static void led1_sampling_indicator(void)
{
    static uint32_t last_blink_time = 0;
    uint32_t current_time = get_system_ms();
    
    if (sampling_state.is_sampling) {
        // 采样状态：500ms周期闪烁
        if (current_time - last_blink_time >= LED_BLINK_PERIOD_MS) {
            led_state_array[LED1_INDEX] = !led_state_array[LED1_INDEX];
            last_blink_time = current_time;
        }
    } else {
        // 非采样状态：常亮
        led_state_array[LED1_INDEX] = 1;
    }
}
```

**LED显示更新**:
- 状态数组统一管理
- GPIO操作计数统计
- 安全边界检查

### 模块交互
- **输入**: 采样状态、超限状态、系统状态
- **输出**: LED硬件控制信号
- **依赖**: GPIO硬件、系统时钟

## 9. 错误处理模块 (error_handler)

### 功能描述
错误处理模块提供系统级错误管理，包括错误检测、分级处理、日志记录、自动恢复等功能，提高系统可靠性。

### 接口定义
```c
// 错误处理初始化
void error_handler_init(void);

// 错误报告
void report_error(system_error_t error_code, error_level_t level, 
                 const char* module, const char* description);

// 错误查询
uint32_t get_error_count(void);
uint32_t get_error_count_by_level(error_level_t level);
const error_record_t* get_last_error(void);
const error_record_t* get_error_log(uint32_t* count);

// 错误恢复
bool attempt_recovery(system_error_t error_code);

// 日志管理
void clear_error_log(void);
```

### 数据结构
```c
// 错误级别枚举
typedef enum {
    ERR_LEVEL_INFO = 0,     // 信息级别
    ERR_LEVEL_WARNING,      // 警告级别
    ERR_LEVEL_ERROR,        // 错误级别
    ERR_LEVEL_CRITICAL      // 严重错误级别
} error_level_t;

// 错误记录结构
typedef struct {
    system_error_t error_code;                    // 错误代码
    error_level_t level;                          // 错误级别
    uint32_t timestamp;                           // 错误时间戳
    char module_name[ERROR_MODULE_NAME_MAX + 1];  // 模块名称
    char description[ERROR_DESC_MAX + 1];         // 错误描述
} error_record_t;

// 错误日志存储
static error_record_t error_log[ERROR_LOG_MAX_SIZE];
static uint32_t error_log_index = 0;
static uint32_t total_error_count = 0;
```

### 关键算法
**错误分级处理**:
1. **INFO**: 记录日志，无需处理
2. **WARNING**: 记录日志，输出警告
3. **ERROR**: 记录日志，尝试恢复
4. **CRITICAL**: 记录日志，系统保护

**自动恢复策略**:
```c
bool attempt_recovery(system_error_t error_code)
{
    switch (error_code) {
        case ERR_SD_INIT_FAILED:
            return recovery_sd_reinit();
        case ERR_OLED_COMM_FAILED:
            return recovery_oled_reinit();
        case ERR_ADC_CALIBRATION_FAILED:
            return recovery_adc_recalibrate();
        default:
            return false;  // 无恢复策略
    }
}
```

### 模块交互
- **输入**: 各模块错误报告、系统状态
- **输出**: 错误日志、恢复操作、状态报告
- **依赖**: 系统时钟、串口输出、存储系统

## 模块间数据流

### 主要数据流向
1. **ADC → OLED**: 电压数据实时显示
2. **ADC → SD**: 采样数据存储
3. **ADC → LED**: 超限状态指示
4. **BTN → ADC**: 采样控制命令
5. **USART → 各模块**: 配置和控制命令
6. **RTC → SD**: 时间戳服务
7. **ERROR → USART**: 错误信息输出

### 配置数据流
- **Flash → 系统**: 配置参数加载
- **USART → Flash**: 配置参数更新
- **系统 → SD**: 配置备份存储

## 接口依赖关系分析

### 模块接口矩阵

| 模块 | scheduler | adc_app | sd_app | oled_app | btn_app | usart_app | rtc_app | led_app | error_handler |
|------|-----------|---------|--------|----------|---------|-----------|---------|---------|---------------|
| **scheduler** | - | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | - |
| **adc_app** | - | - | ✓ | ✓ | - | - | - | ✓ | ✓ |
| **sd_app** | - | - | - | - | - | ✓ | ✓ | - | ✓ |
| **oled_app** | - | ✓ | - | - | - | - | ✓ | - | - |
| **btn_app** | - | ✓ | - | - | - | ✓ | - | - | - |
| **usart_app** | - | ✓ | ✓ | - | - | - | ✓ | - | ✓ |
| **rtc_app** | - | - | ✓ | - | - | - | - | - | - |
| **led_app** | - | ✓ | - | - | - | - | - | - | ✓ |
| **error_handler** | - | - | - | - | - | ✓ | - | - | - |

### 关键数据结构共享

#### 全局配置结构
```c
// 系统配置参数（全局共享）
config_params_t system_config = {
    .ratio_ch0 = 1.0f,      // ADC通道0变比系数
    .limit_ch0 = 1.0f,      // ADC通道0阈值限制
    .sample_period = 5      // 采样周期（秒）
};

// 采样状态（全局共享）
sampling_state_t sampling_state = {
    .is_sampling = 0,       // 采样状态
    .sample_period = 5,     // 采样周期
    .last_sample_time = 0,  // 上次采样时间
    .hide_mode = 0          // 隐藏模式
};

// 存储状态（全局共享）
storage_state_t storage_state = {
    .log_id = 0,            // 当前日志ID
    .sd_card_ready = 0,     // SD卡就绪状态
    .flash_backup_ready = 0 // Flash备份就绪状态
};
```

#### 硬件资源共享
```c
// ADC数据缓冲区
extern uint16_t adc_value[1];

// RTC参数结构
extern rtc_parameter_struct rtc_initpara;

// 串口接收缓冲区
extern uint8_t rxbuffer[512];

// LED状态数组
extern uint8_t led_state_array[6];
```

## 性能特性分析

### 任务执行周期分析

| 任务模块 | 执行周期 | 优先级 | 平均执行时间 | CPU占用率 |
|----------|----------|--------|--------------|-----------|
| btn_task | 5ms | 最高 | <0.1ms | <2% |
| uart_task | 5ms | 最高 | <0.2ms | <4% |
| led_task | 50ms | 中等 | <0.1ms | <0.2% |
| adc_task | 100ms | 中等 | <0.5ms | <0.5% |
| oled_task | 100ms | 中等 | <1ms | <1% |
| rtc_task | 500ms | 低 | <0.1ms | <0.02% |

**总CPU占用率**: ~8% (正常工作负载)

### 内存使用分析

#### 静态内存分配
```c
// 调度器模块
static task_t scheduler_task[6];           // 144 bytes
static uint32_t scheduler_run_count;       // 4 bytes

// ADC模块
static uint32_t adc_sample_count;          // 4 bytes
static uint16_t last_adc_value;            // 2 bytes
static uint32_t next_sample_time;          // 4 bytes

// 存储模块
static char line_buffer[128];              // 128 bytes
static char data_buffer[128];              // 128 bytes
static char log_entry_buffer[256];         // 256 bytes

// OLED模块
static float last_displayed_voltage;       // 4 bytes
static uint32_t last_displayed_time;       // 4 bytes
static uint8_t voltage_updated;            // 1 byte

// 错误处理模块
static error_record_t error_log[50];       // ~2KB
static uint32_t error_log_index;           // 4 bytes
static uint32_t total_error_count;         // 4 bytes
```

**总静态内存**: ~3KB

#### 动态内存使用
- **堆栈使用**: 每个函数调用 <100 bytes
- **临时缓冲区**: 格式化输出 ~512 bytes
- **DMA缓冲区**: 各外设专用缓冲区

## 实时性分析

### 响应时间要求

| 事件类型 | 响应时间要求 | 实际响应时间 | 实现方式 |
|----------|--------------|--------------|----------|
| 按键响应 | <50ms | ~5ms | 5ms轮询 |
| ADC采样 | <1ms | <0.5ms | DMA传输 |
| 串口接收 | <10ms | ~5ms | DMA+中断 |
| OLED更新 | <100ms | ~100ms | 定时更新 |
| 文件写入 | <100ms | ~50ms | 缓冲写入 |

### 中断优先级配置
```c
// 中断优先级分配（数值越小优先级越高）
USART0_IRQn     // 优先级 0 (最高) - 串口空闲中断
DMA1_Channel2   // 优先级 1 - 串口DMA接收
DMA1_Channel0   // 优先级 2 - ADC DMA传输
SysTick_IRQn    // 优先级 3 - 系统时钟
```

## 错误处理策略

### 错误分类和处理

#### 硬件错误
```c
// SD卡相关错误
ERR_SD_INIT_FAILED      → 重新初始化SD卡
ERR_SD_MOUNT_FAILED     → 切换到Flash存储
ERR_FILE_WRITE_FAILED   → 重试写入操作

// ADC相关错误
ERR_ADC_TIMEOUT         → 重新启动ADC
ERR_ADC_DATA_INVALID    → 丢弃无效数据
ERR_ADC_CALIBRATION_FAILED → 重新校准ADC

// 通信错误
ERR_OLED_COMM_FAILED    → 重新初始化I2C
ERR_UART_TX_FAILED      → 重新发送数据
ERR_I2C_BUS_ERROR       → 复位I2C总线
```

#### 软件错误
```c
// 参数错误
ERR_INVALID_PARAMETER   → 使用默认值
ERR_BUFFER_OVERFLOW     → 清空缓冲区
ERR_CONFIG_INVALID      → 恢复默认配置

// 系统错误
ERR_MEMORY_ALLOCATION   → 释放内存重试
ERR_TIMEOUT             → 重新执行操作
ERR_RESOURCE_BUSY       → 等待资源释放
```

### 错误恢复机制

#### 自动恢复流程
```mermaid
graph TD
    A[错误检测] --> B{错误级别}
    B -->|INFO| C[记录日志]
    B -->|WARNING| D[记录日志 + 警告输出]
    B -->|ERROR| E[记录日志 + 尝试恢复]
    B -->|CRITICAL| F[记录日志 + 系统保护]

    E --> G{恢复成功?}
    G -->|是| H[继续运行]
    G -->|否| I[降级运行]

    F --> J[安全模式]
```

#### 恢复策略实现
```c
bool recovery_sd_reinit(void)
{
    // 重新初始化SD卡
    sd_fatfs_init();
    // 验证文件系统
    return (storage_state.sd_card_ready == 1);
}

bool recovery_oled_reinit(void)
{
    // 重新初始化OLED
    bsp_oled_init_impl();
    OLED_Init();
    return true;  // 假设成功
}

bool recovery_adc_recalibrate(void)
{
    // 重新校准ADC
    adc_calibration_enable(ADC0);
    return true;  // 假设成功
}
```

## 配置管理机制

### 配置参数层次结构

#### 编译时配置（my_config.h）
```c
// 硬件配置
#define ADC_CHANNEL_COUNT       1
#define LED_COUNT               6
#define BUFFER_SIZE_MAX         512

// 任务周期配置
#define TASK_PERIOD_ADC_MS      100
#define TASK_PERIOD_LED_MS      50
#define TASK_PERIOD_OLED_MS     100

// Flash地址配置
#define FLASH_CONFIG_ADDR       0x1000
#define FLASH_DEVICE_ID_ADDR    0x2000
```

#### 运行时配置（Flash存储）
```c
typedef struct {
    float ratio_ch0;        // ADC变比系数
    float limit_ch0;        // 阈值限制
    uint32_t sample_period; // 采样周期
    uint32_t crc32;         // 配置校验码
} config_params_t;
```

#### 动态配置（串口命令）
```c
// 支持的配置命令
"set ratio 1.5"     → 设置ADC变比系数
"set limit 2.0"     → 设置阈值限制
"set period 10"     → 设置采样周期
"set device_id ABC" → 设置设备ID
```

### 配置同步机制

#### 配置更新流程
```mermaid
graph LR
    A[串口命令] --> B[参数解析]
    B --> C[参数验证]
    C --> D[内存更新]
    D --> E[Flash保存]
    E --> F[确认反馈]
```

#### 配置加载优先级
1. **Flash配置** (最高优先级)
2. **默认配置** (Flash无效时)
3. **编译配置** (硬件相关)

## 调试和监控接口

### 调试信息输出

#### 系统状态监控
```c
void system_comprehensive_status(void)
{
    // 基本系统信息
    uint32_t power_count = bsp_rtc_get_power_count();
    uint32_t log_id = storage_state.log_id;

    // 性能统计
    uint32_t scheduler_runs = scheduler_get_run_count();
    uint32_t adc_samples = adc_get_sample_count();
    uint32_t gpio_ops = led_get_gpio_operations();

    // 错误统计
    uint32_t total_errors = get_error_count();
    uint32_t critical_errors = get_error_count_by_level(ERR_LEVEL_CRITICAL);
}
```

#### 模块状态查询
```c
// 各模块提供的状态查询接口
scheduler_get_run_count()    // 调度器运行次数
adc_get_sample_count()       // ADC采样次数
led_get_gpio_operations()    // LED操作次数
get_error_count()            // 错误总数
show_current_log_status()    // 存储状态
```

### 性能分析工具

#### perf_counter集成
```c
// 性能测量示例
__cycleof__("ADC Sampling") {
    adc_task();
}

__cpu_usage__(10) {
    scheduler_run();
}
```

#### 资源使用监控
```c
// 内存使用监控
extern uint32_t __heap_base;
extern uint32_t __heap_limit;
uint32_t heap_used = get_heap_usage();

// Flash使用监控
extern uint32_t __text_start__;
extern uint32_t __text_end__;
uint32_t flash_used = &__text_end__ - &__text_start__;
```

## 总结

本系统的9个功能模块采用了清晰的分层架构和模块化设计，每个模块职责单一，接口明确，便于维护和扩展。通过统一的配置管理、完善的错误处理和高效的任务调度，系统具备了良好的可靠性和实时性，能够满足数据采集与存储的各项需求。

### 设计优势

1. **模块化架构**: 清晰的模块边界，便于独立开发和测试
2. **统一接口**: 标准化的函数命名和参数传递
3. **错误处理**: 分级错误处理和自动恢复机制
4. **性能优化**: 预计算、缓存、DMA等优化技术
5. **可配置性**: 多层次配置管理，支持运行时调整
6. **可调试性**: 丰富的调试接口和性能监控工具

### 技术特色

1. **双重存储**: SD卡主存储+Flash备份，确保数据安全
2. **实时调度**: 时间片轮询调度，满足实时性要求
3. **状态同步**: 模块间状态同步机制，确保数据一致性
4. **资源管理**: 高效的内存和外设资源管理
5. **扩展性**: 预留接口和模块化设计支持功能扩展

该系统设计为嵌入式数据采集系统提供了一个完整、可靠、高效的解决方案，具有很好的工程实用价值和技术参考意义。
