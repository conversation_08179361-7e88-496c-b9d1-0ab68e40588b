#ifndef __ADC_APP_H_
#define __ADC_APP_H_

#include "stdint.h"

// ADC采样相关常量定义
#define ADC_MAX_VALUE           4095.0f     ///< 12位ADC最大值
#define ADC_REFERENCE_VOLTAGE   3.3f        ///< ADC参考电压(V)
#define ADC_INVALID_VALUE       0xFFFF      ///< ADC无效值标识

void adc_task(void);

uint32_t adc_get_sample_count(void);

void adc_reset_stats(void);

uint8_t adc_is_data_ready(void);

float adc_get_current_voltage(void);

#endif /* __ADC_APP_H_ */
