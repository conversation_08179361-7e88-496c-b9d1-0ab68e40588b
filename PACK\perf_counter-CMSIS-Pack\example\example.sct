#! armclang --target=arm-arm-none-eabi -mcpu=cortex-m0 -E -xc
; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************


#define RAM1_SIZE    0x00020000
#define RAM1_BASE    0x20000000
#define RAM1_LIMIT   (RAM1_BASE + RAM1_SIZE)

#define STACK_SIZE   0x1000

#define HEAP_ALIGN   8
#define HEAP_SIZE    (RAM1_LIMIT - AlignExpr(ImageLimit(ZI_RAM_UNINIT), HEAP_ALIGN))


LR_IROM1 0x00000000 0x00040000  {    ; load region size_region
    ER_IROM1 0x00000000 0x00040000  {  ; load address = execution address
        *.o (RESET, +First)
        *(InRoot$$Sections)
        .ANY (+RO)
        .ANY (+XO)
    }

    ARM_LIB_STACK RAM1_BASE ALIGN 8 EMPTY FILL 0xDEADBEEF STACK_SIZE {}

    RW_IRAM1 +0  {  ; RW data
        .ANY (+RW +ZI)
    }
    
    ZI_RAM_UNINIT +0 UNINIT {
        .ANY (.bss.noinit)
    }

    ARM_LIB_HEAP +0 ALIGN HEAP_ALIGN EMPTY HEAP_SIZE {}

    ScatterAssert(ImageLimit(ARM_LIB_HEAP) <= RAM1_LIMIT)
}


