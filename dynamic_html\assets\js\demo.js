// 系统演示功能JavaScript文件

// 演示系统状态
let demoState = {
    isRunning: false,
    samplePeriod: 5,
    adcRatio: 1.0,
    currentVoltage: 0.0,
    sampleCount: 0,
    voltageHistory: [],
    maxHistoryLength: 50,
    chart: null,
    animationId: null,
    // 新增演示模式
    demoMode: 'normal', // normal, stress, error, calibration
    errorSimulation: false,
    performanceMode: false,
    // 新增状态
    systemErrors: [],
    cpuUsage: 0,
    memoryUsage: 0,
    temperature: 25.0,
    // 数据导出
    exportData: [],
    lastExportTime: null
};

// 系统配置
const systemConfig = {
    adcMaxValue: 4095,
    referenceVoltage: 3.3,
    voltageLimit: 3.0,
    updateInterval: 100, // ms
    chartUpdateInterval: 1000, // ms
    // 新增配置
    temperatureLimit: 85.0,
    memoryLimit: 80.0,
    cpuLimit: 90.0,
    errorRate: 0.001, // 错误发生概率
    noiseLevel: 0.1, // 噪声水平
    driftRate: 0.01 // 漂移率
};

// 演示模式配置
const demoModes = {
    normal: {
        name: '正常模式',
        description: '标准采样演示',
        noiseLevel: 0.1,
        errorRate: 0.001,
        voltageRange: [2.0, 3.0]
    },
    stress: {
        name: '压力测试',
        description: '高频采样和负载测试',
        noiseLevel: 0.3,
        errorRate: 0.01,
        voltageRange: [0.5, 3.5]
    },
    error: {
        name: '错误模拟',
        description: '模拟各种系统错误',
        noiseLevel: 0.5,
        errorRate: 0.1,
        voltageRange: [0.0, 4.0]
    },
    calibration: {
        name: '校准模式',
        description: '精密校准演示',
        noiseLevel: 0.02,
        errorRate: 0.0001,
        voltageRange: [2.45, 2.55]
    }
};

// DOM加载完成后初始化演示
document.addEventListener('DOMContentLoaded', function() {
    initializeDemo();
});

// 初始化演示系统
function initializeDemo() {
    initializeDemoControls();
    initializeVirtualDisplay();
    initializeLEDPanel();
    initializeVoltageChart();
    initializeDemoModes();
    initializeSystemMonitor();
    initializeDataExport();

    console.log('系统演示模块已初始化');
}

// 初始化演示控制面板
function initializeDemoControls() {
    const startBtn = document.getElementById('startSampling');
    const stopBtn = document.getElementById('stopSampling');
    const periodSlider = document.getElementById('samplePeriod');
    const ratioSlider = document.getElementById('adcRatio');
    const periodValue = document.getElementById('periodValue');
    const ratioValue = document.getElementById('ratioValue');

    // 开始采样按钮
    if (startBtn) {
        startBtn.addEventListener('click', startSampling);
    }

    // 停止采样按钮
    if (stopBtn) {
        stopBtn.addEventListener('click', stopSampling);
    }

    // 采样周期滑块
    if (periodSlider && periodValue) {
        periodSlider.addEventListener('input', function() {
            demoState.samplePeriod = parseInt(this.value);
            periodValue.textContent = this.value;
        });
    }

    // ADC变比滑块
    if (ratioSlider && ratioValue) {
        ratioSlider.addEventListener('input', function() {
            demoState.adcRatio = parseFloat(this.value);
            ratioValue.textContent = this.value;
        });
    }
}

// 初始化演示模式选择
function initializeDemoModes() {
    const modeSelector = document.getElementById('demoMode');
    if (modeSelector) {
        // 填充模式选项
        Object.keys(demoModes).forEach(modeKey => {
            const option = document.createElement('option');
            option.value = modeKey;
            option.textContent = demoModes[modeKey].name;
            modeSelector.appendChild(option);
        });

        // 监听模式变化
        modeSelector.addEventListener('change', function() {
            changeDemoMode(this.value);
        });
    }

    // 初始化错误模拟开关
    const errorToggle = document.getElementById('errorSimulation');
    if (errorToggle) {
        errorToggle.addEventListener('change', function() {
            demoState.errorSimulation = this.checked;
            updateSystemStatus();
        });
    }

    // 初始化性能模式开关
    const perfToggle = document.getElementById('performanceMode');
    if (perfToggle) {
        perfToggle.addEventListener('change', function() {
            demoState.performanceMode = this.checked;
            updateSystemStatus();
        });
    }
}

// 切换演示模式
function changeDemoMode(mode) {
    if (!demoModes[mode]) return;

    demoState.demoMode = mode;
    const modeConfig = demoModes[mode];

    // 更新系统配置
    systemConfig.noiseLevel = modeConfig.noiseLevel;
    systemConfig.errorRate = modeConfig.errorRate;

    // 更新UI显示
    const modeDesc = document.getElementById('modeDescription');
    if (modeDesc) {
        modeDesc.textContent = modeConfig.description;
    }

    // 重置错误状态
    demoState.systemErrors = [];
    updateErrorDisplay();

    console.log('切换到演示模式:', modeConfig.name);
}

// 初始化系统监控
function initializeSystemMonitor() {
    // 初始化系统状态
    demoState.cpuUsage = 15 + Math.random() * 10;
    demoState.memoryUsage = 20 + Math.random() * 15;
    demoState.temperature = 25 + Math.random() * 5;

    // 定期更新系统状态
    setInterval(updateSystemStatus, 2000);
}

// 更新系统状态
function updateSystemStatus() {
    // 模拟CPU使用率变化
    if (demoState.isRunning) {
        demoState.cpuUsage += (Math.random() - 0.5) * 5;
        demoState.cpuUsage = Math.max(10, Math.min(95, demoState.cpuUsage));
    } else {
        demoState.cpuUsage = Math.max(5, demoState.cpuUsage - 2);
    }

    // 模拟内存使用率变化
    if (demoState.performanceMode) {
        demoState.memoryUsage += (Math.random() - 0.3) * 3;
    } else {
        demoState.memoryUsage += (Math.random() - 0.5) * 2;
    }
    demoState.memoryUsage = Math.max(15, Math.min(85, demoState.memoryUsage));

    // 模拟温度变化
    const targetTemp = demoState.isRunning ? 35 + Math.random() * 10 : 25 + Math.random() * 5;
    demoState.temperature += (targetTemp - demoState.temperature) * 0.1;

    // 更新显示
    updateMonitorDisplay();

    // 检查错误条件
    checkSystemErrors();
}

// 更新监控显示
function updateMonitorDisplay() {
    const cpuDisplay = document.getElementById('cpuUsage');
    const memDisplay = document.getElementById('memoryUsage');
    const tempDisplay = document.getElementById('temperature');

    if (cpuDisplay) {
        cpuDisplay.textContent = demoState.cpuUsage.toFixed(1) + '%';
        cpuDisplay.className = demoState.cpuUsage > systemConfig.cpuLimit ? 'status-error' : 'status-normal';
    }

    if (memDisplay) {
        memDisplay.textContent = demoState.memoryUsage.toFixed(1) + '%';
        memDisplay.className = demoState.memoryUsage > systemConfig.memoryLimit ? 'status-error' : 'status-normal';
    }

    if (tempDisplay) {
        tempDisplay.textContent = demoState.temperature.toFixed(1) + '°C';
        tempDisplay.className = demoState.temperature > systemConfig.temperatureLimit ? 'status-error' : 'status-normal';
    }
}

// 检查系统错误
function checkSystemErrors() {
    const currentTime = Date.now();

    // 随机错误生成
    if (demoState.errorSimulation && Math.random() < systemConfig.errorRate) {
        generateRandomError();
    }

    // 检查阈值错误
    if (demoState.cpuUsage > systemConfig.cpuLimit) {
        addSystemError('CPU_OVERLOAD', 'CPU使用率过高: ' + demoState.cpuUsage.toFixed(1) + '%');
    }

    if (demoState.memoryUsage > systemConfig.memoryLimit) {
        addSystemError('MEMORY_FULL', '内存使用率过高: ' + demoState.memoryUsage.toFixed(1) + '%');
    }

    if (demoState.temperature > systemConfig.temperatureLimit) {
        addSystemError('OVERHEATING', '系统过热: ' + demoState.temperature.toFixed(1) + '°C');
    }

    // 清理过期错误
    demoState.systemErrors = demoState.systemErrors.filter(error =>
        currentTime - error.timestamp < 30000 // 30秒后清理
    );

    updateErrorDisplay();
}

// 生成随机错误
function generateRandomError() {
    const errorTypes = [
        { code: 'ADC_NOISE', message: 'ADC信号噪声过大' },
        { code: 'SD_WRITE_FAIL', message: 'SD卡写入失败' },
        { code: 'OLED_COMM_ERROR', message: 'OLED通信错误' },
        { code: 'RTC_DRIFT', message: 'RTC时钟漂移' },
        { code: 'FLASH_VERIFY_FAIL', message: 'Flash验证失败' },
        { code: 'UART_OVERFLOW', message: '串口缓冲区溢出' }
    ];

    const randomError = errorTypes[Math.floor(Math.random() * errorTypes.length)];
    addSystemError(randomError.code, randomError.message);
}

// 添加系统错误
function addSystemError(code, message) {
    const existingError = demoState.systemErrors.find(error => error.code === code);
    if (existingError) {
        existingError.count++;
        existingError.timestamp = Date.now();
    } else {
        demoState.systemErrors.push({
            code: code,
            message: message,
            timestamp: Date.now(),
            count: 1
        });
    }
}

// 更新错误显示
function updateErrorDisplay() {
    const errorList = document.getElementById('systemErrors');
    if (!errorList) return;

    if (demoState.systemErrors.length === 0) {
        errorList.innerHTML = '<div class="no-errors">系统运行正常</div>';
    } else {
        errorList.innerHTML = demoState.systemErrors.map(error => `
            <div class="error-item">
                <span class="error-code">${error.code}</span>
                <span class="error-message">${error.message}</span>
                <span class="error-count">${error.count > 1 ? `(${error.count})` : ''}</span>
            </div>
        `).join('');
    }
}

// 初始化数据导出功能
function initializeDataExport() {
    const exportBtn = document.getElementById('exportData');
    const clearBtn = document.getElementById('clearData');

    if (exportBtn) {
        exportBtn.addEventListener('click', exportSampleData);
    }

    if (clearBtn) {
        clearBtn.addEventListener('click', clearSampleData);
    }

    // 定期保存数据到导出缓存
    setInterval(updateExportData, 5000);
}

// 更新导出数据
function updateExportData() {
    if (demoState.isRunning && demoState.voltageHistory.length > 0) {
        const latestData = demoState.voltageHistory[demoState.voltageHistory.length - 1];
        demoState.exportData.push({
            timestamp: latestData.time.toISOString(),
            voltage: latestData.voltage,
            sampleNumber: latestData.sample,
            mode: demoState.demoMode,
            cpuUsage: demoState.cpuUsage,
            memoryUsage: demoState.memoryUsage,
            temperature: demoState.temperature,
            errors: demoState.systemErrors.length
        });

        // 限制导出数据大小
        if (demoState.exportData.length > 1000) {
            demoState.exportData = demoState.exportData.slice(-500);
        }
    }
}

// 导出采样数据
function exportSampleData() {
    if (demoState.exportData.length === 0) {
        alert('没有可导出的数据');
        return;
    }

    const exportFormat = document.getElementById('exportFormat')?.value || 'csv';

    let content, filename, mimeType;

    if (exportFormat === 'csv') {
        content = generateCSVData();
        filename = `gd32_demo_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
        mimeType = 'text/csv';
    } else if (exportFormat === 'json') {
        content = JSON.stringify(demoState.exportData, null, 2);
        filename = `gd32_demo_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        mimeType = 'application/json';
    }

    // 创建下载链接
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    demoState.lastExportTime = new Date();
    updateExportStatus();

    console.log('数据已导出:', filename);
}

// 生成CSV格式数据
function generateCSVData() {
    const headers = [
        'Timestamp',
        'Voltage(V)',
        'Sample Number',
        'Demo Mode',
        'CPU Usage(%)',
        'Memory Usage(%)',
        'Temperature(°C)',
        'Error Count'
    ];

    const csvContent = [
        headers.join(','),
        ...demoState.exportData.map(row => [
            row.timestamp,
            row.voltage.toFixed(3),
            row.sampleNumber,
            row.mode,
            row.cpuUsage.toFixed(1),
            row.memoryUsage.toFixed(1),
            row.temperature.toFixed(1),
            row.errors
        ].join(','))
    ].join('\n');

    return csvContent;
}

// 初始化数据导出功能
function initializeDataExport() {
    const exportBtn = document.getElementById('exportData');
    const clearBtn = document.getElementById('clearData');

    if (exportBtn) {
        exportBtn.addEventListener('click', exportSampleData);
    }

    if (clearBtn) {
        clearBtn.addEventListener('click', clearSampleData);
    }

    // 定期保存数据到导出缓存
    setInterval(updateExportData, 5000);
}

// 更新导出数据
function updateExportData() {
    if (demoState.isRunning && demoState.voltageHistory.length > 0) {
        const latestData = demoState.voltageHistory[demoState.voltageHistory.length - 1];
        demoState.exportData.push({
            timestamp: latestData.time.toISOString(),
            voltage: latestData.voltage,
            sampleNumber: latestData.sample,
            mode: demoState.demoMode,
            cpuUsage: demoState.cpuUsage,
            memoryUsage: demoState.memoryUsage,
            temperature: demoState.temperature,
            errors: demoState.systemErrors.length
        });

        // 限制导出数据大小
        if (demoState.exportData.length > 1000) {
            demoState.exportData = demoState.exportData.slice(-500);
        }
    }
}

// 导出采样数据
function exportSampleData() {
    if (demoState.exportData.length === 0) {
        alert('没有可导出的数据');
        return;
    }

    const exportFormat = document.getElementById('exportFormat')?.value || 'csv';

    let content, filename, mimeType;

    if (exportFormat === 'csv') {
        content = generateCSVData();
        filename = `gd32_demo_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
        mimeType = 'text/csv';
    } else if (exportFormat === 'json') {
        content = JSON.stringify(demoState.exportData, null, 2);
        filename = `gd32_demo_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        mimeType = 'application/json';
    }

    // 创建下载链接
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    demoState.lastExportTime = new Date();
    updateExportStatus();

    console.log('数据已导出:', filename);
}

// 生成CSV格式数据
function generateCSVData() {
    const headers = [
        'Timestamp',
        'Voltage(V)',
        'Sample Number',
        'Demo Mode',
        'CPU Usage(%)',
        'Memory Usage(%)',
        'Temperature(°C)',
        'Error Count'
    ];

    const csvContent = [
        headers.join(','),
        ...demoState.exportData.map(row => [
            row.timestamp,
            row.voltage.toFixed(3),
            row.sampleNumber,
            row.mode,
            row.cpuUsage.toFixed(1),
            row.memoryUsage.toFixed(1),
            row.temperature.toFixed(1),
            row.errors
        ].join(','))
    ].join('\n');

    return csvContent;
}

// 清空采样数据
function clearSampleData() {
    if (confirm('确定要清空所有采样数据吗？')) {
        demoState.exportData = [];
        demoState.voltageHistory = [];
        demoState.sampleCount = 0;

        // 清空图表
        if (demoState.chart) {
            demoState.chart.data.labels = [];
            demoState.chart.data.datasets[0].data = [];
            demoState.chart.data.datasets[1].data = [];
            demoState.chart.update();
        }

        updateExportStatus();
        console.log('采样数据已清空');
    }
}

// 更新导出状态显示
function updateExportStatus() {
    const statusElement = document.getElementById('exportStatus');
    if (statusElement) {
        const dataCount = demoState.exportData.length;
        const lastExport = demoState.lastExportTime ?
            demoState.lastExportTime.toLocaleString() : '从未导出';

        statusElement.innerHTML = `
            <div>数据条数: ${dataCount}</div>
            <div>最后导出: ${lastExport}</div>
        `;
    }
}

// 开始采样
function startSampling() {
    if (demoState.isRunning) return;

    demoState.isRunning = true;
    demoState.sampleCount = 0;
    demoState.voltageHistory = [];

    // 更新按钮状态
    const startBtn = document.getElementById('startSampling');
    const stopBtn = document.getElementById('stopSampling');
    
    if (startBtn) {
        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 采样中...';
    }
    
    if (stopBtn) {
        stopBtn.disabled = false;
    }

    // 更新OLED显示
    updateOLEDDisplay('采样模式', '正在初始化...');

    // 启动LED1闪烁（采样状态指示）
    startLEDBlink('led1');

    // 开始采样循环
    startSamplingLoop();

    console.log('开始采样，周期:', demoState.samplePeriod, '秒');
}

// 停止采样
function stopSampling() {
    if (!demoState.isRunning) return;

    demoState.isRunning = false;

    // 更新按钮状态
    const startBtn = document.getElementById('startSampling');
    const stopBtn = document.getElementById('stopSampling');
    
    if (startBtn) {
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-play"></i> 开始采样';
    }
    
    if (stopBtn) {
        stopBtn.disabled = true;
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> 停止采样';
    }

    // 更新OLED显示
    updateOLEDDisplay('system idle', '');

    // 停止所有LED闪烁
    stopAllLEDBlink();

    // 停止采样循环
    if (demoState.animationId) {
        cancelAnimationFrame(demoState.animationId);
        demoState.animationId = null;
    }

    console.log('停止采样，总采样次数:', demoState.sampleCount);
}

// 采样循环
function startSamplingLoop() {
    let lastSampleTime = Date.now();
    let lastChartUpdate = Date.now();

    function sampleLoop() {
        if (!demoState.isRunning) return;

        const currentTime = Date.now();

        // 检查是否需要采样
        if (currentTime - lastSampleTime >= demoState.samplePeriod * 1000) {
            performSample();
            lastSampleTime = currentTime;
        }

        // 检查是否需要更新图表
        if (currentTime - lastChartUpdate >= systemConfig.chartUpdateInterval) {
            updateVoltageChart();
            lastChartUpdate = currentTime;
        }

        // 持续更新显示
        updateRealTimeDisplay();

        demoState.animationId = requestAnimationFrame(sampleLoop);
    }

    sampleLoop();
}

// 执行一次采样（增强版）
function performSample() {
    // 根据演示模式生成电压值
    const modeConfig = demoModes[demoState.demoMode];
    const [minVoltage, maxVoltage] = modeConfig.voltageRange;

    let baseVoltage;

    // 根据模式生成基础电压
    switch (demoState.demoMode) {
        case 'normal':
            baseVoltage = 2.5 + Math.sin(Date.now() / 10000) * 0.3;
            break;
        case 'stress':
            baseVoltage = minVoltage + Math.random() * (maxVoltage - minVoltage);
            break;
        case 'error':
            baseVoltage = Math.random() < 0.1 ?
                Math.random() * maxVoltage :
                2.5 + (Math.random() - 0.5) * 0.8;
            break;
        case 'calibration':
            baseVoltage = 2.5 + (Math.random() - 0.5) * 0.1;
            break;
        default:
            baseVoltage = 2.5;
    }

    // 添加噪声
    const noise = (Math.random() - 0.5) * modeConfig.noiseLevel;
    let rawVoltage = baseVoltage + noise;

    // 添加系统漂移
    if (demoState.sampleCount > 0) {
        const drift = (Math.random() - 0.5) * systemConfig.driftRate;
        rawVoltage += drift;
    }

    // 限制电压范围
    rawVoltage = Math.max(0, Math.min(systemConfig.referenceVoltage, rawVoltage));

    // 应用变比系数
    demoState.currentVoltage = rawVoltage * demoState.adcRatio;

    // 增加采样计数
    demoState.sampleCount++;

    // 添加到历史记录
    const timestamp = new Date();
    demoState.voltageHistory.push({
        time: timestamp,
        voltage: demoState.currentVoltage,
        sample: demoState.sampleCount,
        mode: demoState.demoMode
    });

    // 限制历史记录长度
    if (demoState.voltageHistory.length > demoState.maxHistoryLength) {
        demoState.voltageHistory.shift();
    }

    // 检查超限
    const isOverLimit = demoState.currentVoltage > systemConfig.voltageLimit;

    if (isOverLimit) {
        // 启动LED2（超限报警）
        setLEDState('led2', true);
        addSystemError('VOLTAGE_OVERLIMIT', `电压超限: ${demoState.currentVoltage.toFixed(2)}V`);
        console.log('电压超限:', demoState.currentVoltage.toFixed(2), 'V');
    } else {
        setLEDState('led2', false);
    }

    // 模拟ADC错误
    if (Math.random() < modeConfig.errorRate) {
        addSystemError('ADC_SAMPLE_ERROR', 'ADC采样异常');
    }

    // 更新OLED显示
    const timeStr = timestamp.toLocaleTimeString();
    const voltageStr = demoState.currentVoltage.toFixed(3) + 'V';
    const modeStr = `[${demoState.demoMode.toUpperCase()}]`;
    updateOLEDDisplay(`${timeStr} ${modeStr}`, voltageStr);

    console.log('采样 #' + demoState.sampleCount + ':', voltageStr, isOverLimit ? '(超限)' : '', `[${demoState.demoMode}]`);
}

// 更新实时显示
function updateRealTimeDisplay() {
    // 这里可以添加其他实时更新的内容
    // 例如系统状态LED的随机闪烁等
    
    if (demoState.isRunning && Math.random() < 0.01) {
        // 随机闪烁系统状态LED
        const systemLEDs = ['led3', 'led4', 'led5', 'led6'];
        const randomLED = systemLEDs[Math.floor(Math.random() * systemLEDs.length)];
        flashLED(randomLED, 200);
    }
}

// 初始化虚拟OLED显示
function initializeVirtualDisplay() {
    updateOLEDDisplay('system idle', '');
}

// 更新OLED显示
function updateOLEDDisplay(line1, line2) {
    const oledLine1 = document.getElementById('oledLine1');
    const oledLine2 = document.getElementById('oledLine2');

    if (oledLine1) {
        oledLine1.textContent = line1;
        oledLine1.classList.add('fade-in');
        setTimeout(() => oledLine1.classList.remove('fade-in'), 300);
    }

    if (oledLine2) {
        oledLine2.textContent = line2;
        oledLine2.classList.add('fade-in');
        setTimeout(() => oledLine2.classList.remove('fade-in'), 300);
    }
}

// 初始化LED面板
function initializeLEDPanel() {
    // 初始化所有LED为关闭状态
    for (let i = 1; i <= 6; i++) {
        setLEDState(`led${i}`, false);
    }
}

// 设置LED状态
function setLEDState(ledId, isOn) {
    const led = document.getElementById(ledId);
    if (led) {
        if (isOn) {
            led.classList.add('on');
            led.classList.remove('blink');
        } else {
            led.classList.remove('on', 'blink');
        }
    }
}

// 开始LED闪烁
function startLEDBlink(ledId) {
    const led = document.getElementById(ledId);
    if (led) {
        led.classList.add('on', 'blink');
    }
}

// 停止所有LED闪烁
function stopAllLEDBlink() {
    for (let i = 1; i <= 6; i++) {
        const led = document.getElementById(`led${i}`);
        if (led) {
            led.classList.remove('on', 'blink');
        }
    }
}

// LED短暂闪烁
function flashLED(ledId, duration = 200) {
    const led = document.getElementById(ledId);
    if (led) {
        led.classList.add('on');
        setTimeout(() => {
            led.classList.remove('on');
        }, duration);
    }
}

// 初始化电压图表
function initializeVoltageChart() {
    const canvas = document.getElementById('voltageChart');
    if (!canvas || typeof Chart === 'undefined') {
        console.warn('Chart.js未加载或canvas元素不存在');
        return;
    }

    const ctx = canvas.getContext('2d');
    
    demoState.chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '电压值 (V)',
                data: [],
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointRadius: 3,
                pointHoverRadius: 5
            }, {
                label: '超限阈值',
                data: [],
                borderColor: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderWidth: 2,
                borderDash: [5, 5],
                fill: false,
                pointRadius: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '时间'
                    },
                    ticks: {
                        maxTicksLimit: 10
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '电压 (V)'
                    },
                    min: 0,
                    max: 4,
                    ticks: {
                        stepSize: 0.5
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y.toFixed(2) + 'V';
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            animation: {
                duration: 300
            }
        }
    });
}

// 更新电压图表
function updateVoltageChart() {
    if (!demoState.chart || demoState.voltageHistory.length === 0) return;

    const chart = demoState.chart;
    const history = demoState.voltageHistory;

    // 更新标签和数据
    chart.data.labels = history.map(item => 
        item.time.toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        })
    );

    // 更新电压数据
    chart.data.datasets[0].data = history.map(item => item.voltage);

    // 更新阈值线
    chart.data.datasets[1].data = history.map(() => systemConfig.voltageLimit);

    // 更新图表
    chart.update('none'); // 无动画更新以提高性能
}

// 模拟系统状态变化
function simulateSystemActivity() {
    if (!demoState.isRunning) return;

    // 随机系统活动
    if (Math.random() < 0.1) {
        const activities = [
            () => flashLED('led3', 150),
            () => flashLED('led4', 150),
            () => flashLED('led5', 150),
            () => flashLED('led6', 150)
        ];

        const randomActivity = activities[Math.floor(Math.random() * activities.length)];
        randomActivity();
    }
}

// 重置演示系统
function resetDemo() {
    stopSampling();
    demoState.sampleCount = 0;
    demoState.voltageHistory = [];
    demoState.currentVoltage = 0.0;

    // 重置图表
    if (demoState.chart) {
        demoState.chart.data.labels = [];
        demoState.chart.data.datasets[0].data = [];
        demoState.chart.data.datasets[1].data = [];
        demoState.chart.update();
    }

    // 重置显示
    updateOLEDDisplay('system idle', '');
    initializeLEDPanel();

    console.log('演示系统已重置');
}

// 清空采样数据
function clearSampleData() {
    if (confirm('确定要清空所有采样数据吗？')) {
        demoState.exportData = [];
        demoState.voltageHistory = [];
        demoState.sampleCount = 0;

        // 清空图表
        if (demoState.chart) {
            demoState.chart.data.labels = [];
            demoState.chart.data.datasets[0].data = [];
            demoState.chart.data.datasets[1].data = [];
            demoState.chart.update();
        }

        updateExportStatus();
        console.log('采样数据已清空');
    }
}

// 更新导出状态显示
function updateExportStatus() {
    const statusElement = document.getElementById('exportStatus');
    if (statusElement) {
        const dataCount = demoState.exportData.length;
        const lastExport = demoState.lastExportTime ?
            demoState.lastExportTime.toLocaleString() : '从未导出';

        statusElement.innerHTML = `
            <div>数据条数: ${dataCount}</div>
            <div>最后导出: ${lastExport}</div>
        `;
    }
}

// 增强的图表更新功能
function updateVoltageChart() {
    if (!demoState.chart || demoState.voltageHistory.length === 0) return;

    const chart = demoState.chart;
    const history = demoState.voltageHistory;

    // 更新标签和数据
    chart.data.labels = history.map(item =>
        item.time.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        })
    );

    // 更新电压数据
    chart.data.datasets[0].data = history.map(item => item.voltage);

    // 更新阈值线
    chart.data.datasets[1].data = history.map(() => systemConfig.voltageLimit);

    // 根据演示模式调整图表颜色
    const modeColors = {
        normal: '#2563eb',
        stress: '#f59e0b',
        error: '#ef4444',
        calibration: '#10b981'
    };

    chart.data.datasets[0].borderColor = modeColors[demoState.demoMode] || '#2563eb';
    chart.data.datasets[0].backgroundColor = (modeColors[demoState.demoMode] || '#2563eb') + '20';

    // 添加实时数据流效果
    if (demoState.isRunning) {
        chart.options.animation = {
            duration: 300,
            easing: 'easeInOutQuart'
        };
    } else {
        chart.options.animation = false;
    }

    // 更新图表
    chart.update('none');
}

// 导出函数供外部调用
window.demoSystem = {
    start: startSampling,
    stop: stopSampling,
    reset: resetDemo,
    getState: () => ({ ...demoState }),
    setConfig: (config) => Object.assign(systemConfig, config),
    // 新增功能
    changeMode: changeDemoMode,
    exportData: exportSampleData,
    clearData: clearSampleData,
    getModes: () => ({ ...demoModes }),
    getErrors: () => [...demoState.systemErrors],
    clearErrors: () => {
        demoState.systemErrors = [];
        updateErrorDisplay();
    }
};

// 定期模拟系统活动
setInterval(simulateSystemActivity, 1000);