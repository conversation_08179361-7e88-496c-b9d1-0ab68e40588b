<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 60" width="200" height="60">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="chipGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 芯片图标 -->
  <rect x="10" y="15" width="30" height="30" rx="4" fill="url(#chipGradient)" stroke="#f59e0b" stroke-width="2"/>
  <circle cx="17" cy="22" r="2" fill="#f59e0b"/>
  <circle cx="25" cy="22" r="2" fill="#f59e0b"/>
  <circle cx="33" cy="22" r="2" fill="#f59e0b"/>
  <circle cx="17" cy="30" r="2" fill="#f59e0b"/>
  <circle cx="25" cy="30" r="2" fill="#f59e0b"/>
  <circle cx="33" cy="30" r="2" fill="#f59e0b"/>
  <circle cx="17" cy="38" r="2" fill="#f59e0b"/>
  <circle cx="25" cy="38" r="2" fill="#f59e0b"/>
  <circle cx="33" cy="38" r="2" fill="#f59e0b"/>
  
  <!-- 连接线 -->
  <line x1="5" y1="20" x2="10" y2="20" stroke="#f59e0b" stroke-width="2"/>
  <line x1="5" y1="25" x2="10" y2="25" stroke="#f59e0b" stroke-width="2"/>
  <line x1="5" y1="30" x2="10" y2="30" stroke="#f59e0b" stroke-width="2"/>
  <line x1="5" y1="35" x2="10" y2="35" stroke="#f59e0b" stroke-width="2"/>
  <line x1="5" y1="40" x2="10" y2="40" stroke="#f59e0b" stroke-width="2"/>
  
  <line x1="40" y1="20" x2="45" y2="20" stroke="#f59e0b" stroke-width="2"/>
  <line x1="40" y1="25" x2="45" y2="25" stroke="#f59e0b" stroke-width="2"/>
  <line x1="40" y1="30" x2="45" y2="30" stroke="#f59e0b" stroke-width="2"/>
  <line x1="40" y1="35" x2="45" y2="35" stroke="#f59e0b" stroke-width="2"/>
  <line x1="40" y1="40" x2="45" y2="40" stroke="#f59e0b" stroke-width="2"/>
  
  <!-- 文字 -->
  <text x="55" y="25" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="url(#logoGradient)">GD32F470</text>
  <text x="55" y="42" font-family="Arial, sans-serif" font-size="12" fill="#64748b">数据采集系统</text>
</svg>
