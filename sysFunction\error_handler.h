#ifndef __ERROR_HANDLER_H__
#define __ERROR_HANDLER_H__

#include "stdint.h"
#include "stdbool.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum {
    ERR_NONE = 0,                    // 无错误
    
    // 存储相关错误 (1-20)
    ERR_SD_INIT_FAILED = 1,          // SD卡初始化失败
    ERR_SD_MOUNT_FAILED = 2,         // SD卡挂载失败
    ERR_FILE_OPEN_FAILED = 3,        // 文件打开失败
    ERR_FILE_WRITE_FAILED = 4,       // 文件写入失败
    ERR_FILE_READ_FAILED = 5,        // 文件读取失败
    ERR_FLASH_WRITE_FAILED = 6,      // Flash写入失败
    ERR_FLASH_READ_FAILED = 7,       // Flash读取失败
    
    // ADC相关错误 (21-40)
    ERR_ADC_TIMEOUT = 21,            // ADC转换超时
    ERR_ADC_DATA_INVALID = 22,       // ADC数据无效
    ERR_ADC_CALIBRATION_FAILED = 23, // ADC校准失败
    
    // 通信相关错误 (41-60)
    ERR_OLED_COMM_FAILED = 41,       // OLED通信失败
    ERR_OLED_INIT_FAILED = 42,       // OLED初始化失败
    ERR_UART_TX_FAILED = 43,         // 串口发送失败
    ERR_UART_RX_TIMEOUT = 44,        // 串口接收超时
    ERR_I2C_BUS_ERROR = 45,          // I2C总线错误
    ERR_SPI_COMM_ERROR = 46,         // SPI通信错误
    
    // 系统相关错误 (61-80)
    ERR_MEMORY_ALLOCATION = 61,      // 内存分配失败
    ERR_INVALID_PARAMETER = 62,      // 无效参数
    ERR_BUFFER_OVERFLOW = 63,        // 缓冲区溢出
    ERR_TIMEOUT = 64,                // 操作超时
    ERR_RESOURCE_BUSY = 65,          // 资源忙
    ERR_HARDWARE_FAULT = 66,         // 硬件故障
    
    // 配置相关错误 (81-100)
    ERR_CONFIG_INVALID = 81,         // 配置无效
    ERR_CONFIG_LOAD_FAILED = 82,     // 配置加载失败
    ERR_CONFIG_SAVE_FAILED = 83,     // 配置保存失败
    
    ERR_MAX_ERROR_CODE = 255         // 最大错误码
} system_error_t;

typedef enum {
    ERR_LEVEL_INFO = 0,              // 信息级别
    ERR_LEVEL_WARNING = 1,           // 警告级别
    ERR_LEVEL_ERROR = 2,             // 错误级别
    ERR_LEVEL_CRITICAL = 3           // 严重错误级别
} error_level_t;

typedef struct {
    system_error_t error_code;       // 错误码
    error_level_t level;             // 错误级别
    uint32_t timestamp;              // 错误发生时间戳
    char module_name[16];            // 发生错误的模块名称
    char description[64];            // 错误描述
} error_record_t;

// 错误处理配置常量
#define ERROR_LOG_MAX_SIZE      32   // 错误日志最大条数
#define ERROR_MODULE_NAME_MAX   15   // 模块名称最大长度
#define ERROR_DESC_MAX          63   // 错误描述最大长度

// 错误处理核心接口
void error_handler_init(void);
void report_error(system_error_t error_code, error_level_t level, const char* module, const char* description);
bool attempt_recovery(system_error_t error_code);
const char* get_error_string(system_error_t error_code);

// 错误统计和查询接口
uint32_t get_error_count(void);
uint32_t get_error_count_by_level(error_level_t level);
const error_record_t* get_last_error(void);
const error_record_t* get_error_log(uint32_t* count);
void clear_error_log(void);

// 便捷的错误报告宏
#define REPORT_INFO(module, desc)     report_error(ERR_NONE, ERR_LEVEL_INFO, module, desc)
#define REPORT_WARNING(code, module, desc) report_error(code, ERR_LEVEL_WARNING, module, desc)
#define REPORT_ERROR(code, module, desc)   report_error(code, ERR_LEVEL_ERROR, module, desc)
#define REPORT_CRITICAL(code, module, desc) report_error(code, ERR_LEVEL_CRITICAL, module, desc)

// 错误恢复策略接口
bool recovery_sd_reinit(void);
bool recovery_oled_reinit(void);
bool recovery_adc_recalibrate(void);

#ifdef __cplusplus
}
#endif

#endif /* __ERROR_HANDLER_H__ */
