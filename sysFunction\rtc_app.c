#include "my_config.h"

extern rtc_parameter_struct rtc_initpara;
extern __IO uint32_t prescaler_a, prescaler_s;
extern uint32_t RTCSRC_FLAG;

uint8_t bcd_to_dec(uint8_t bcd)
{
    return ((bcd >> 4) * 10) + (bcd & 0x0F);
}

uint8_t dec_to_bcd(uint8_t dec)
{
    return ((dec / 10) << 4) | (dec % 10);
}

uint8_t is_leap_year(uint16_t year)
{
    return ((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0);
}

uint8_t get_days_in_month(uint8_t month, uint16_t year)
{
    const uint8_t days_in_month[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    
    if (month == 2 && is_leap_year(year)) {
        return 29;
    }
    return days_in_month[month - 1];
}

#define TIMEZONE_OFFSET_HOURS 8
#define TIMEZONE_OFFSET_SECONDS (TIMEZONE_OFFSET_HOURS * 3600)

local_time_t timestamp_to_local_time(uint32_t timestamp)
{
    local_time_t local_time = {0};
    
    uint32_t local_timestamp = timestamp + TIMEZONE_OFFSET_SECONDS;
    
    uint32_t days_since_epoch = local_timestamp / 86400;
    uint32_t seconds_in_day = local_timestamp % 86400;
    
    uint16_t year = 1970;
    uint32_t remaining_days = days_since_epoch;
    uint32_t days_in_year;
    
    while (remaining_days >= (days_in_year = (is_leap_year(year) ? 366 : 365))) {
        remaining_days -= days_in_year;
        year++;
    }
    
    uint8_t month = 1;
    uint32_t days_in_month_val;
    
    while (remaining_days >= (days_in_month_val = get_days_in_month(month, year))) {
        remaining_days -= days_in_month_val;
        month++;
    }
    
    uint8_t day = remaining_days + 1;
    
    uint8_t hour = seconds_in_day / 3600;
    uint8_t minute = (seconds_in_day % 3600) / 60;
    uint8_t second = seconds_in_day % 60;
    
    local_time.year = year;
    local_time.month = month;
    local_time.day = day;
    local_time.hour = hour;
    local_time.minute = minute;
    local_time.second = second;
    
    return local_time;
}

uint32_t get_unix_timestamp(void)
{
    extern rtc_parameter_struct rtc_initpara;
    
    rtc_current_time_get(&rtc_initpara);
    
    uint16_t year = 2000 + bcd_to_dec(rtc_initpara.year);
    uint8_t month = bcd_to_dec(rtc_initpara.month);
    uint8_t day = bcd_to_dec(rtc_initpara.date);
    uint8_t hour = bcd_to_dec(rtc_initpara.hour);
    uint8_t minute = bcd_to_dec(rtc_initpara.minute);
    uint8_t second = bcd_to_dec(rtc_initpara.second);
    
    const uint16_t epoch_year = 1970;
    
    if (year < epoch_year) {
        return 0;
    }
    
    uint32_t days = 0;
    for (uint16_t y = epoch_year; y < year; y++) {
        days += is_leap_year(y) ? 366 : 365;
    }
    
    for (uint8_t m = 1; m < month; m++) {
        days += get_days_in_month(m, year);
    }
    
    days += (day - 1);
    
    uint32_t timestamp = days * 24 * 3600;
    timestamp += hour * 3600;
    timestamp += minute * 60;
    timestamp += second;
    
    return timestamp;
}

void rtc_task(void)
{

}

static void bsp_rtc_pre_cfg(void)
{
    #if defined (RTC_CLOCK_SOURCE_IRC32K)
          rcu_osci_on(RCU_IRC32K);
          rcu_osci_stab_wait(RCU_IRC32K);
          rcu_rtc_clock_config(RCU_RTCSRC_IRC32K);

          prescaler_s = 0x13F;
          prescaler_a = 0x63;
    #elif defined (RTC_CLOCK_SOURCE_LXTAL)
          rcu_osci_on(RCU_LXTAL);
          rcu_osci_stab_wait(RCU_LXTAL);
          rcu_rtc_clock_config(RCU_RTCSRC_LXTAL);

          prescaler_s = 0xFF;
          prescaler_a = 0x7F;
    #else
    #error RTC clock source should be defined.
    #endif /* RTC_CLOCK_SOURCE_IRC32K */

    rcu_periph_clock_enable(RCU_RTC);
    rtc_register_sync_wait();
}

static int bsp_rtc_setup(void)
{
    int ret = 0;

    rtc_initpara.factor_asyn = prescaler_a;
    rtc_initpara.factor_syn = prescaler_s;
    rtc_initpara.year = RTC_INIT_YEAR;
    rtc_initpara.day_of_week = RTC_TUESDAY;  // 2025年1月1日是星期三
    rtc_initpara.month = RTC_INIT_MONTH;
    rtc_initpara.date = RTC_INIT_DATE;
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;

    /* 设置默认时间为2025-01-01 00:00:00 */
    rtc_initpara.hour = RTC_INIT_HOUR;
    rtc_initpara.minute = RTC_INIT_MINUTE;
    rtc_initpara.second = RTC_INIT_SECOND;

    /* RTC时间配置 */
    if(ERROR == rtc_init(&rtc_initpara)){
        ret = -1;
    } else {
        /* 设置初始化标识，表示RTC已正确初始化 */
        RTC_BKP0 = RTC_FIRST_INIT_FLAG;

        /* 初始化上电次数为0 */
        RTC_POWER_COUNT_BKP = 0;
    }
    return ret;
}

int bsp_rtc_init(void)
{
    int ret = 0;

    /* 使能PMU时钟，允许访问备份域寄存器 */
    rcu_periph_clock_enable(RCU_PMU);
    pmu_backup_write_enable();

    /* RTC预配置（时钟源、分频器等） */
    bsp_rtc_pre_cfg();

    /* 获取RTC时钟源选择 */
    RTCSRC_FLAG = GET_BITS(RCU_BDCTL, 8, 9);

    /* 检查是否需要重新初始化RTC */
    if (bsp_rtc_need_init()) {
        /* 首次上电或数据丢失，需要初始化RTC */
        ret = bsp_rtc_setup();
        if (ret == 0) {
            /* 初始化成功，设置上电次数为0（从0开始计数，对应log0） */
            RTC_POWER_COUNT_BKP = 0;
        }
    } else {
        /* RTC数据有效，检查是否为重置后的第一次启动 */
        if (RTC_RESET_FLAG_BKP == RTC_RESET_MAGIC) {
            /* 重置后的第一次启动，清除重置标志，保持上电次数为0 */
            RTC_RESET_FLAG_BKP = 0;
            // 上电次数保持为0，不增加
        } else {
            /* 延迟递增：不在此处立即递增，等待系统初始化完成后再递增 */
            // bsp_rtc_increment_power_count(); // 移除立即递增，改为延迟递增
        }
    }

    /* 清除所有复位标志 */
    rcu_all_reset_flag_clear();

    return ret;
}

uint8_t bsp_rtc_need_init(void)
{
    return (RTC_BKP0 != RTC_FIRST_INIT_FLAG);
}

uint32_t bsp_rtc_get_power_count(void)
{
    return RTC_POWER_COUNT_BKP;
}

void bsp_rtc_increment_power_count(void)
{
    uint32_t current_count = RTC_POWER_COUNT_BKP;
    RTC_POWER_COUNT_BKP = current_count + 1;
}

void bsp_rtc_reset_power_count(void)
{
    RTC_POWER_COUNT_BKP = 0;
    RTC_RESET_FLAG_BKP = RTC_RESET_MAGIC;  // 设置重置标志
}

void bsp_rtc_set_power_count(uint32_t count)
{
    RTC_POWER_COUNT_BKP = count;
    if (count == 0) {
        RTC_RESET_FLAG_BKP = RTC_RESET_MAGIC;  // 如果设置为0，设置重置标志
    } else {
        RTC_RESET_FLAG_BKP = 0;  // 清除重置标志
    }
}

void bsp_rtc_complete_reset(void)
{
    RTC_BKP0 = 0;                    // 清除初始化标识
    RTC_POWER_COUNT_BKP = 0;         // 清除上电次数
    RTC_RESET_FLAG_BKP = 0;          // 清除重置标志
}

void bsp_rtc_delayed_increment_power_count(void)
{
    // 检查是否需要递增上电次数
    if (RTC_BKP0 == RTC_FIRST_INIT_FLAG) {
        // 如果是重置后的第一次启动，清除重置标志但不递增
        if (RTC_RESET_FLAG_BKP == RTC_RESET_MAGIC) {
            RTC_RESET_FLAG_BKP = 0;
        } else {
            // 正常情况下递增上电次数，为下次启动准备
            bsp_rtc_increment_power_count();
        }
    }
}
