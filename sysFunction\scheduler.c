
#include "my_config.h"

uint8_t task_num;

static uint32_t scheduler_run_count = 0;

typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
    uint32_t next_run;
} task_t;

static task_t scheduler_task[] =
{
     {adc_task,  TASK_PERIOD_ADC_MS,   0,   TASK_PERIOD_ADC_MS}
    ,{led_task,  TASK_PERIOD_LED_MS,   0,   TASK_PERIOD_LED_MS}
    ,{oled_task, TASK_PERIOD_OLED_MS,  0,   TASK_PERIOD_OLED_MS}
    ,{btn_task,  TASK_PERIOD_BTN_MS,   0,   TASK_PERIOD_BTN_MS}
    ,{uart_task, TASK_PERIOD_UART_MS,  0,   TASK_PERIOD_UART_MS}
    ,{rtc_task,  TASK_PERIOD_RTC_MS,   0,   TASK_PERIOD_RTC_MS}

};

void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

void scheduler_run(void)
{
    scheduler_run_count++;

    uint32_t current_time = get_system_ms();

    for (uint8_t i = 0; i < task_num; i++)
    {
        if (current_time >= scheduler_task[i].next_run)
        {
            scheduler_task[i].last_run = current_time;

            scheduler_task[i].next_run = current_time + scheduler_task[i].rate_ms;

            scheduler_task[i].task_func();
        }
    }
}

uint32_t scheduler_get_run_count(void)
{
    return scheduler_run_count;
}

void scheduler_reset_stats(void)
{
    scheduler_run_count = 0;
}
