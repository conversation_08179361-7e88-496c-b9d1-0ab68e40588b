#ifndef MY_CONFIG_H
#define MY_CONFIG_H

#include "gd32f4xx.h"
#include "gd32f4xx_sdio.h"
#include "gd32f4xx_dma.h"
#include "systick.h"

#include "ebtn.h"
#include "oled.h"
#include "gd25qxx.h"
#include "sdio_sdcard.h"
#include "ff.h"
#include "diskio.h"

#include "sd_app.h"
#include "led_app.h"
#include "adc_app.h"
#include "oled_app.h"
#include "usart_app.h"
#include "rtc_app.h"
#include "btn_app.h"
#include "scheduler.h"
#include "error_handler.h"

// 安全检查相关宏定义
#define SAFETY_CHECK_ENABLE     1           ///< 安全检查总开关：1=启用，0=禁用

// 调试功能相关宏定义
#define DEBUG_LOG_ROUTING       1           ///< 日志路由调试：1=启用，0=禁用
#define HIDE_CRYPTO_DEBUG       1           ///< hide加密调试：1=启用，0=禁用

// 错误代码定义
#define ERR_SUCCESS             0x0000      ///< 操作成功
#define ERR_CRYPTO_FAILED       0x3001      ///< 加密操作失败
#define ERR_CRYPTO_VALIDATION_FAILED 0x3002 ///< 加密验证失败
#define LED_COUNT               6           ///< LED总数量
#define LED_MAX_INDEX           (LED_COUNT-1) ///< LED最大索引值
#define ADC_CHANNEL_COUNT       1           ///< ADC通道数量
#define BUFFER_SIZE_MAX         512         ///< 缓冲区最大大小

// 时间相关常量定义
#define LED_BLINK_PERIOD_MS     500         ///< LED状态切换周期（毫秒），实现1秒完整闪烁周期
#define SAMPLE_PERIOD_TO_MS     1000        ///< 采样周期转换为毫秒的乘数
#define MILLISECONDS_PER_SECOND 1000        ///< 每秒毫秒数

// 任务调度周期常量定义
#define TASK_PERIOD_ADC_MS      100         ///< ADC任务周期（毫秒）
#define TASK_PERIOD_LED_MS      50          ///< LED任务周期（毫秒）
#define TASK_PERIOD_OLED_MS     100         ///< OLED任务周期（毫秒）
#define TASK_PERIOD_BTN_MS      5           ///< 按键任务周期（毫秒）
#define TASK_PERIOD_UART_MS     5           ///< 串口任务周期（毫秒）
#define TASK_PERIOD_RTC_MS      500         ///< RTC任务周期（毫秒）

// 缓冲区大小常量定义
#define LINE_BUFFER_SIZE        128         ///< 行缓冲区大小
#define DATA_BUFFER_SIZE        128         ///< 数据缓冲区大小
#define LOG_ENTRY_BUFFER_SIZE   256         ///< 日志条目缓冲区大小
#define FILENAME_BUFFER_SIZE    64          ///< 文件名缓冲区大小
#define DATETIME_STRING_SIZE    16          ///< 日期时间字符串大小
#define WRITE_BUFFER_SIZE       256         ///< 写入缓冲区大小

// 文件存储相关常量定义
#define MAX_RECORDS_PER_FILE    10          ///< 每个文件最大记录数
#define MAX_STRING_LENGTH       200         ///< 最大字符串长度
#define MAX_VOLTAGE_RANGE       1000.0f     ///< 最大电压范围（V）
#define MIN_VOLTAGE_RANGE       0.0f        ///< 最小电压范围（V）
#define MAX_CONFIG_RATIO        1000.0f     ///< 最大配置变比
#define MIN_CONFIG_RATIO        0.0f        ///< 最小配置变比

// Flash存储相关常量定义 - 统一地址管理
#define FLASH_CONFIG_ADDR       0x1000      ///< 系统配置在Flash中的地址
#define FLASH_DEVICE_ID_ADDR    0x2000      ///< Device ID在Flash中的地址
#define FLASH_LOG0_ADDR         0x3000      ///< Flash log0缓存地址
#define FLASH_BACKUP_ADDR       0x4000      ///< 备份数据在Flash中的地址
#define FLASH_SECTOR_SIZE       0x1000      ///< Flash扇区大小(4KB)
#define MAX_LOG_ID_VALUE        10000       ///< 最大日志ID值
#define FLASH_INVALID_VALUE     0xFFFFFFFF  ///< Flash无效值标识

// LED索引常量定义
#define LED1_INDEX              0           ///< LED1索引
#define LED2_INDEX              1           ///< LED2索引

// 数值转换常量定义
#define VOLTAGE_TO_INT_MULTIPLIER 1000      ///< 电压转整数乘数（已弃用，使用VOLTAGE_FRAC_MULTIPLIER）
#define VOLTAGE_FRAC_MULTIPLIER   65536     ///< 电压小数部分乘数（16.16定点数格式）

// 安全检查宏定义
#if SAFETY_CHECK_ENABLE
    #define SAFETY_CHECK_NULL_PTR(ptr, module) \
        do { \
            if ((ptr) == NULL) { \
                REPORT_ERROR(ERR_INVALID_PARAMETER, module, "Null pointer detected"); \
                return; \
            } \
        } while(0)

    #define SAFETY_CHECK_NULL_PTR_RET(ptr, module, ret_val) \
        do { \
            if ((ptr) == NULL) { \
                REPORT_ERROR(ERR_INVALID_PARAMETER, module, "Null pointer detected"); \
                return (ret_val); \
            } \
        } while(0)

    #define SAFETY_CHECK_ARRAY_BOUNDS(index, max_index, module) \
        do { \
            if ((index) > (max_index)) { \
                REPORT_ERROR(ERR_BUFFER_OVERFLOW, module, "Array index out of bounds"); \
                return; \
            } \
        } while(0)

    #define SAFETY_CHECK_RANGE(value, min_val, max_val, module) \
        do { \
            if ((value) < (min_val) || (value) > (max_val)) { \
                REPORT_ERROR(ERR_INVALID_PARAMETER, module, "Value out of range"); \
                return; \
            } \
        } while(0)
#else
    #define SAFETY_CHECK_NULL_PTR(ptr, module)                    ((void)0)
    #define SAFETY_CHECK_NULL_PTR_RET(ptr, module, ret_val)       ((void)0)
    #define SAFETY_CHECK_ARRAY_BOUNDS(index, max_index, module)   ((void)0)
    #define SAFETY_CHECK_RANGE(value, min_val, max_val, module)   ((void)0)
#endif

#include "perf_counter.h"

#include <stdint.h>
#include <stddef.h>
#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include <stdio.h>

#ifdef __cplusplus
extern "C" {
#endif
/***************************************************************************************************************/
/* LED */
#define LED1_PORT        GPIOD
#define LED1_CLK_PORT    RCU_GPIOD

#define LED_PORT        GPIOB
#define LED_CLK_PORT    RCU_GPIOB

#define LED1_PIN        GPIO_PIN_7
#define LED2_PIN        GPIO_PIN_3
#define LED3_PIN        GPIO_PIN_4
#define LED4_PIN        GPIO_PIN_5
#define LED5_PIN        GPIO_PIN_6
#define LED6_PIN        GPIO_PIN_7

/***************************************************************************************************************/
/* KEY */
#define KEY_PORT        GPIOE
#define KEY_CLK_PORT    RCU_GPIOE
#define KEYW_PORT        GPIOA
#define KEYW_CLK_PORT    RCU_GPIOA

#define KEY1_PIN        GPIO_PIN_7
#define KEY2_PIN        GPIO_PIN_8
#define KEY3_PIN        GPIO_PIN_9
#define KEY4_PIN        GPIO_PIN_10
#define KEY5_PIN        GPIO_PIN_11
#define KEY6_PIN        GPIO_PIN_12
#define KEYW_PIN        GPIO_PIN_0

#define KEY1_READ       gpio_input_bit_get(KEY_PORT, KEY1_PIN)
#define KEY2_READ       gpio_input_bit_get(KEY_PORT, KEY2_PIN)
#define KEY3_READ       gpio_input_bit_get(KEY_PORT, KEY3_PIN)
#define KEY4_READ       gpio_input_bit_get(KEY_PORT, KEY4_PIN)
#define KEY5_READ       gpio_input_bit_get(KEY_PORT, KEY5_PIN)
#define KEY6_READ       gpio_input_bit_get(KEY_PORT, KEY6_PIN)
#define KEYW_READ       gpio_input_bit_get(KEYW_PORT, KEYW_PIN)

/***************************************************************************************************************/

/* OLED */
#define I2C0_OWN_ADDRESS7      0x72
#define I2C0_SLAVE_ADDRESS7    0x82
#define I2C0_DATA_ADDRESS      (uint32_t)&I2C_DATA(I2C0)

#define OLED_PORT        GPIOB
#define OLED_CLK_PORT    RCU_GPIOB
#define OLED_DAT_PIN     GPIO_PIN_9
#define OLED_CLK_PIN     GPIO_PIN_8

// FUNCTION
int bsp_oled_init_impl(void);  // 内部实现函数

/***************************************************************************************************************/

/* gd25qxx */

#define SPI_PORT              GPIOB
#define SPI_CLK_PORT          RCU_GPIOB

#define SPI_NSS               GPIO_PIN_12
#define SPI_SCK               GPIO_PIN_13
#define SPI_MISO              GPIO_PIN_14
#define SPI_MOSI              GPIO_PIN_15

// FUNCTION
int bsp_gd25qxx_init_impl(void);  // 内部实现函数

/***************************************************************************************************************/

/* USART */
#define DEBUG_USART               (USART0)
#define USART0_RDATA_ADDRESS      ((uint32_t)&USART_DATA(USART0))

#define USART_PORT                GPIOA
#define USART_CLK_PORT            RCU_GPIOA

#define USART_TX                  GPIO_PIN_9
#define USART_RX                  GPIO_PIN_10

// FUNCTION
int bsp_usart_init_impl(void);  // 内部实现函数

/***************************************************************************************************************/

/* ADC */
#define ADC1_PORT       GPIOC
#define ADC1_CLK_PORT   RCU_GPIOC

#define ADC1_PIN        GPIO_PIN_0

// FUNCTION
void bsp_adc_init(void);

/***************************************************************************************************************/

/* RTC */
//#define RTC_CLOCK_SOURCE_LXTAL  // 注释掉LXTAL，使用IRC32K
#define RTC_CLOCK_SOURCE_IRC32K
#define BKP_VALUE               0x32F0      ///< RTC备份寄存器标识值
#define RTC_FIRST_INIT_FLAG     0x32F0      ///< RTC首次初始化标识
#define RTC_POWER_COUNT_BKP     RTC_BKP1    ///< 上电次数存储在备份寄存器1
#define RTC_RESET_FLAG_BKP      RTC_BKP2    ///< 重置标志存储在备份寄存器2
#define RTC_RESET_MAGIC         0xABCD      ///< 重置标志魔数
#define RTC_INIT_YEAR           0x25        ///< 默认初始化年份(2025)
#define RTC_INIT_MONTH          RTC_JAN     ///< 默认初始化月份(1月)
#define RTC_INIT_DATE           0x01        ///< 默认初始化日期(1日)
#define RTC_INIT_HOUR           0x00        ///< 默认初始化小时(0时)
#define RTC_INIT_MINUTE         0x00        ///< 默认初始化分钟(0分)
#define RTC_INIT_SECOND         0x00        ///< 默认初始化秒(0秒)

// FUNCTION DECLARATIONS
void bsp_led_init(void);                    // LED模块初始化 (在led_app.c中实现)
void bsp_btn_init(void);                    // 按键模块初始化 (在btn_app.c中实现)
int bsp_rtc_init(void);                     // RTC模块初始化 (在rtc_app.c中实现)
uint8_t bsp_rtc_need_init(void);            // RTC初始化检查 (在rtc_app.c中实现)
uint32_t bsp_rtc_get_power_count(void);     // 获取上电次数 (在rtc_app.c中实现)
void bsp_rtc_increment_power_count(void);   // 增加上电次数 (在rtc_app.c中实现)
void bsp_rtc_reset_power_count(void);       // 重置上电次数 (在rtc_app.c中实现)
void bsp_rtc_set_power_count(uint32_t count); // 设置上电次数 (在rtc_app.c中实现)
void bsp_rtc_complete_reset(void);           // 完全重置RTC备份寄存器 (在rtc_app.c中实现)
void bsp_rtc_delayed_increment_power_count(void); // 延迟递增上电次数 (在rtc_app.c中实现)

// BSP内部实现函数声明 (在my_config.c中实现)
int bsp_oled_init_impl(void);               // OLED模块初始化实现
int bsp_gd25qxx_init_impl(void);            // SPI Flash模块初始化实现
int bsp_usart_init_impl(void);              // 串口模块初始化实现
int bsp_adc_init_impl(void);                // ADC模块初始化实现

/***************************************************************************************************************/

#ifdef __cplusplus
  }
#endif

#endif /* MY_CONFIG_H */
