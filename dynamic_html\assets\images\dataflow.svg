<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 900 500" width="900" height="500">
  <defs>
    <linearGradient id="inputGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="outputGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="900" height="500" fill="#f8fafc"/>
  
  <!-- 标题 -->
  <text x="450" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1e293b">数据采集与处理流程</text>
  
  <!-- ADC输入 -->
  <rect x="50" y="100" width="120" height="60" rx="8" fill="url(#inputGradient)" filter="url(#shadow)"/>
  <text x="110" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">ADC输入</text>
  <text x="110" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">PC0 (0-3.3V)</text>
  
  <!-- ADC采样模块 -->
  <rect x="220" y="100" width="120" height="60" rx="8" fill="url(#processGradient)" filter="url(#shadow)"/>
  <text x="280" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">ADC采样</text>
  <text x="280" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">12位精度</text>
  
  <!-- 数据处理 -->
  <rect x="390" y="100" width="120" height="60" rx="8" fill="url(#processGradient)" filter="url(#shadow)"/>
  <text x="450" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">数据处理</text>
  <text x="450" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">电压计算</text>
  
  <!-- 超限检测 -->
  <rect x="560" y="100" width="120" height="60" rx="8" fill="url(#processGradient)" filter="url(#shadow)"/>
  <text x="620" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">超限检测</text>
  <text x="620" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">阈值比较</text>
  
  <!-- OLED显示 -->
  <rect x="300" y="220" width="120" height="60" rx="8" fill="url(#outputGradient)" filter="url(#shadow)"/>
  <text x="360" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">OLED显示</text>
  <text x="360" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">实时数据</text>
  
  <!-- LED指示 -->
  <rect x="480" y="220" width="120" height="60" rx="8" fill="url(#outputGradient)" filter="url(#shadow)"/>
  <text x="540" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">LED指示</text>
  <text x="540" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">状态显示</text>
  
  <!-- SD卡存储 -->
  <rect x="660" y="220" width="120" height="60" rx="8" fill="url(#outputGradient)" filter="url(#shadow)"/>
  <text x="720" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">SD卡存储</text>
  <text x="720" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">主存储</text>
  
  <!-- Flash备份 -->
  <rect x="660" y="320" width="120" height="60" rx="8" fill="url(#outputGradient)" filter="url(#shadow)"/>
  <text x="720" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Flash备份</text>
  <text x="720" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">备份存储</text>
  
  <!-- 串口输出 -->
  <rect x="120" y="320" width="120" height="60" rx="8" fill="url(#outputGradient)" filter="url(#shadow)"/>
  <text x="180" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">串口输出</text>
  <text x="180" y="365" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">调试信息</text>
  
  <!-- 按键输入 -->
  <rect x="50" y="220" width="120" height="60" rx="8" fill="url(#inputGradient)" filter="url(#shadow)"/>
  <text x="110" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">按键输入</text>
  <text x="110" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">用户控制</text>
  
  <!-- 连接线 -->
  <!-- 主数据流 -->
  <line x1="170" y1="130" x2="220" y2="130" stroke="#64748b" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="340" y1="130" x2="390" y2="130" stroke="#64748b" stroke-width="3" marker-end="url(#arrowhead)"/>
  <line x1="510" y1="130" x2="560" y2="130" stroke="#64748b" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- 分支输出 -->
  <line x1="450" y1="160" x2="360" y2="220" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="620" y1="160" x2="540" y2="220" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="620" y1="160" x2="720" y2="220" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="720" y1="280" x2="720" y2="320" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 控制流 -->
  <line x1="170" y1="250" x2="280" y2="160" stroke="#ef4444" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  <line x1="280" y1="160" x2="180" y2="320" stroke="#ef4444" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  
  <!-- 数据流标签 -->
  <text x="195" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">原始数据</text>
  <text x="365" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">数字量</text>
  <text x="535" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">电压值</text>
  
  <!-- 图例 -->
  <rect x="50" y="420" width="800" height="60" rx="8" fill="rgba(255,255,255,0.9)" stroke="#e2e8f0" stroke-width="1"/>
  <text x="70" y="440" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1e293b">图例说明</text>
  
  <line x1="70" y1="455" x2="100" y2="455" stroke="#64748b" stroke-width="3"/>
  <text x="110" y="460" font-family="Arial, sans-serif" font-size="12" fill="#64748b">主数据流</text>
  
  <line x1="200" y1="455" x2="230" y2="455" stroke="#ef4444" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="240" y="460" font-family="Arial, sans-serif" font-size="12" fill="#64748b">控制流</text>
  
  <rect x="320" y="450" width="15" height="10" fill="url(#inputGradient)"/>
  <text x="345" y="460" font-family="Arial, sans-serif" font-size="12" fill="#64748b">输入模块</text>
  
  <rect x="430" y="450" width="15" height="10" fill="url(#processGradient)"/>
  <text x="455" y="460" font-family="Arial, sans-serif" font-size="12" fill="#64748b">处理模块</text>
  
  <rect x="540" y="450" width="15" height="10" fill="url(#outputGradient)"/>
  <text x="565" y="460" font-family="Arial, sans-serif" font-size="12" fill="#64748b">输出模块</text>
</svg>
