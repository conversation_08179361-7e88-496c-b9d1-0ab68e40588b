#include "my_config.h"
#include "usart_app.h"
#include "adc_app.h"
#include "usart_app.h"
#include "ff.h"



FIL fdst;
uint16_t i = 0, count, result = 0;
UINT br, bw;
sd_card_info_struct sd_cardinfo;
BYTE buffer[DATA_BUFFER_SIZE];
BYTE filebuffer[DATA_BUFFER_SIZE];

FATFS fs;
extern config_params_t system_config;
extern storage_state_t storage_state;
extern sampling_state_t sampling_state;

FIL current_log_file;
FIL current_sample_file;
FIL current_overlimit_file;
FIL current_hidedata_file;

uint8_t log_file_open = 0;
uint8_t sample_file_open = 0;
uint8_t overlimit_file_open = 0;
uint8_t hidedata_file_open = 0;

// Flash日志存储机制 - 专门存储第一次上电的日志
#define MAX_FLASH_LOG_ENTRIES   30          ///< Flash最大日志条目数
#define MAX_LOG_ENTRY_LENGTH    100         ///< 每条日志最大长度
// 使用统一的Flash地址管理
#define FLASH_LOG_ADDR          FLASH_LOG0_ADDR  // Flash log0专用区域，与Device_ID分离      ///< Flash日志存储地址
#define FLASH_LOG_MAGIC         0x4C4F4730  ///< Flash日志魔数 "LOG0"

typedef struct {
    uint32_t magic;                                              ///< 魔数，用于验证有效性
    uint16_t entry_count;                                        ///< 当前日志条目数
    uint16_t reserved;                                           ///< 保留字段
    char entries[MAX_FLASH_LOG_ENTRIES][MAX_LOG_ENTRY_LENGTH];  ///< 日志条目数组
} flash_log0_storage_t;

static flash_log0_storage_t flash_log0 = {0};

uint32_t voltage_to_encrypted_hex(float voltage)
{
    // 安全检查：验证电压值范围
    if (voltage < MIN_VOLTAGE_RANGE || voltage > MAX_VOLTAGE_RANGE) {
        REPORT_WARNING(ERR_INVALID_PARAMETER, "hide_crypto", "Voltage value out of range");
        return 0;
    }

    // 16.16定点数格式加密
    uint16_t voltage_int = (uint16_t)voltage;
    uint16_t voltage_frac = (uint16_t)((voltage - voltage_int) * VOLTAGE_FRAC_MULTIPLIER);

    uint32_t result = (voltage_int << 16) | voltage_frac;

    #ifdef HIDE_CRYPTO_DEBUG
    char debug_msg[128];
    sprintf(debug_msg, "Encrypt: %.3fV -> int=%u, frac=%u, hex=0x%08X",
            voltage, voltage_int, voltage_frac, result);
    REPORT_INFO("hide_crypto", debug_msg);
    #endif

    return result;
}

float decrypt_voltage_hex(uint32_t hex_value)
{
    uint16_t voltage_int = (hex_value >> 16) & 0xFFFF;
    uint16_t voltage_frac = hex_value & 0xFFFF;

    float result = voltage_int + ((float)voltage_frac / VOLTAGE_FRAC_MULTIPLIER);

    #ifdef HIDE_CRYPTO_DEBUG
    char debug_msg[128];
    sprintf(debug_msg, "Decrypt: 0x%08X -> int=%u, frac=%u, voltage=%.3fV",
            hex_value, voltage_int, voltage_frac, result);
    REPORT_INFO("hide_crypto", debug_msg);
    #endif

    return result;
}

void load_flash_log0(void)
{
    spi_flash_buffer_read((uint8_t *)&flash_log0, FLASH_LOG_ADDR, sizeof(flash_log0_storage_t));

    // 验证魔数，如果无效则初始化
    if (flash_log0.magic != FLASH_LOG_MAGIC) {
        memset(&flash_log0, 0, sizeof(flash_log0_storage_t));
        flash_log0.magic = FLASH_LOG_MAGIC;
        flash_log0.entry_count = 0;
    }
}

void save_flash_log0(void)
{
    spi_flash_sector_erase(FLASH_LOG_ADDR);
    spi_flash_wait_for_write_end();
    spi_flash_buffer_write((uint8_t *)&flash_log0, FLASH_LOG_ADDR, sizeof(flash_log0_storage_t));
    spi_flash_wait_for_write_end();  // 确保写入完成
}

void clear_flash_log0(void)
{
    spi_flash_sector_erase(FLASH_LOG_ADDR);
    spi_flash_wait_for_write_end();
    memset(&flash_log0, 0, sizeof(flash_log0_storage_t));
    flash_log0.magic = FLASH_LOG_MAGIC;
    flash_log0.entry_count = 0;
}

void add_log_to_flash_log0(const char *log_entry)
{
    load_flash_log0();

    // 添加日志条目
    if (flash_log0.entry_count < MAX_FLASH_LOG_ENTRIES) {
        strncpy(flash_log0.entries[flash_log0.entry_count], log_entry, MAX_LOG_ENTRY_LENGTH - 1);
        flash_log0.entries[flash_log0.entry_count][MAX_LOG_ENTRY_LENGTH - 1] = '\0';
        flash_log0.entry_count++;

        // 保存到Flash
        save_flash_log0();
    }
}

uint8_t has_flash_log0(void)
{
    load_flash_log0();
    return (flash_log0.magic == FLASH_LOG_MAGIC && flash_log0.entry_count > 0);
}

void verify_log_files_integrity(void)
{
    uint32_t current_power_count = bsp_rtc_get_power_count();
    uint32_t current_log_id = storage_state.log_id;

    my_printf(DEBUG_USART, "=== Log Files Integrity Check ===\r\n");
    my_printf(DEBUG_USART, "Current power count: %u\r\n", current_power_count);
    my_printf(DEBUG_USART, "Current log ID: %u\r\n", current_log_id);
    my_printf(DEBUG_USART, "Expected current file: log%u.txt\r\n", current_log_id);

    // 检查SD卡状态
    DSTATUS sd_status = disk_status(0);
    my_printf(DEBUG_USART, "SD card status: %s\r\n", (sd_status == RES_OK) ? "AVAILABLE" : "NOT AVAILABLE");

    if (sd_status != RES_OK) {
        my_printf(DEBUG_USART, "Cannot check files: SD card not available\r\n");
        my_printf(DEBUG_USART, "==================================\r\n");
        return;
    }

    uint32_t existing_files = 0;
    uint32_t missing_files = 0;
    uint32_t total_size = 0;

    // 检查log0.txt到当前power_count的所有文件
    for (uint32_t i = 0; i <= current_power_count; i++) {
        char filename[64];
        sprintf(filename, "log/log%u.txt", i);

        FIL test_file;
        FRESULT result = f_open(&test_file, filename, FA_READ);
        if (result == FR_OK) {
            DWORD size = f_size(&test_file);  // 使用DWORD替代FSIZE_t
            f_close(&test_file);
            my_printf(DEBUG_USART, "%s: EXISTS (%lu bytes)", filename, (unsigned long)size);

            // 标记当前活动文件
            if (i == current_log_id) {
                my_printf(DEBUG_USART, " [CURRENT]");
            }
            my_printf(DEBUG_USART, "\r\n");

            existing_files++;
            total_size += size;
        } else {
            my_printf(DEBUG_USART, "%s: MISSING", filename);
            if (i == current_log_id) {
                my_printf(DEBUG_USART, " [CURRENT - PROBLEM!]");
            }
            my_printf(DEBUG_USART, "\r\n");
            missing_files++;
        }
    }

    // 统计信息
    my_printf(DEBUG_USART, "\r\nSummary:\r\n");
    my_printf(DEBUG_USART, "  Total files expected: %u\r\n", current_power_count + 1);
    my_printf(DEBUG_USART, "  Files existing: %u\r\n", existing_files);
    my_printf(DEBUG_USART, "  Files missing: %u\r\n", missing_files);
    my_printf(DEBUG_USART, "  Total log size: %lu bytes\r\n", (unsigned long)total_size);

    // 完整性评估
    if (missing_files == 0) {
        my_printf(DEBUG_USART, "  Integrity: GOOD (all files present)\r\n");
    } else if (missing_files <= current_power_count / 2) {
        my_printf(DEBUG_USART, "  Integrity: PARTIAL (some files missing)\r\n");
    } else {
        my_printf(DEBUG_USART, "  Integrity: POOR (many files missing)\r\n");
    }

    my_printf(DEBUG_USART, "==================================\r\n");
}

/**
 * @brief 列出log目录下的所有文件
 */
void list_log_files(void)
{
    DIR dir;
    FILINFO fno;
    FRESULT result;

    result = f_opendir(&dir, "log");
    if (result == FR_OK) {
        my_printf(DEBUG_USART, "=== Log Directory Contents ===\r\n");
        while (f_readdir(&dir, &fno) == FR_OK && fno.fname[0]) {
            my_printf(DEBUG_USART, "%s (%lu bytes)\r\n", fno.fname, (unsigned long)fno.fsize);
        }
        // f_closedir(&dir);  // 某些FatFS版本不需要显式关闭目录
        my_printf(DEBUG_USART, "==============================\r\n");
    } else {
        my_printf(DEBUG_USART, "Failed to open log directory\r\n");
    }
}

void debug_flash_log0_status(void)
{
    load_flash_log0();

    my_printf(DEBUG_USART, "=== Flash Log0 Debug ===\r\n");
    my_printf(DEBUG_USART, "Magic: 0x%08X (Expected: 0x%08X)\r\n", flash_log0.magic, FLASH_LOG_MAGIC);
    my_printf(DEBUG_USART, "Entry Count: %u\r\n", flash_log0.entry_count);

    if (flash_log0.magic == FLASH_LOG_MAGIC && flash_log0.entry_count > 0) {
        my_printf(DEBUG_USART, "Flash log0 entries:\r\n");
        uint16_t show_count = (flash_log0.entry_count > 5) ? 5 : flash_log0.entry_count;
        for (uint16_t i = 0; i < show_count; i++) {
            my_printf(DEBUG_USART, "[%u]: %s", i, flash_log0.entries[i]);
        }
        if (flash_log0.entry_count > 5) {
            my_printf(DEBUG_USART, "... +%u more entries\r\n", flash_log0.entry_count - 5);
        }
    } else {
        my_printf(DEBUG_USART, "No valid flash log0 found\r\n");
    }
    my_printf(DEBUG_USART, "========================\r\n");
}

void ensure_log0_exists(void)
{
    FIL log0_file;
    FRESULT result = f_open(&log0_file, "log/log0.txt", FA_OPEN_EXISTING);

    if (result != FR_OK) {
        // log0.txt不存在，创建空文件
        result = f_open(&log0_file, "log/log0.txt", FA_CREATE_NEW | FA_WRITE);
        if (result == FR_OK) {
            // 写入基本的启动信息
            uint32_t timestamp = get_unix_timestamp();
            local_time_t local_time = timestamp_to_local_time(timestamp);

            char init_log[LOG_ENTRY_BUFFER_SIZE];
            sprintf(init_log, "%04d-%02d-%02d %02d:%02d:%02d log0.txt created (recovery)\r\n",
                    local_time.year, local_time.month, local_time.day,
                    local_time.hour, local_time.minute, local_time.second);

            UINT bytes_written;
            f_write(&log0_file, init_log, strlen(init_log), &bytes_written);
            f_sync(&log0_file);
            f_close(&log0_file);
            REPORT_INFO("sd_app", "log0.txt created for recovery");
        } else {
            REPORT_ERROR(ERR_FILE_OPEN_FAILED, "sd_app", "Failed to create log0.txt");
        }
    } else {
        f_close(&log0_file);
        REPORT_INFO("sd_app", "log0.txt already exists");
    }
}

void write_flash_log0_to_sd(void)
{
    if (!has_flash_log0()) {
        REPORT_INFO("sd_app", "No flash log0 to write");
        return;
    }

    load_flash_log0();

    // 创建log0.txt文件（追加模式，不覆盖已有内容）
    FIL log0_file;
    FRESULT result = f_open(&log0_file, "log/log0.txt", FA_OPEN_ALWAYS | FA_WRITE);

    if (result == FR_OK) {
        // 移动到文件末尾，确保追加而不是覆盖
        f_lseek(&log0_file, f_size(&log0_file));

        uint8_t all_successful = 1;

        // 写入所有日志条目
        for (uint16_t i = 0; i < flash_log0.entry_count; i++) {
            UINT bytes_written;
            FRESULT write_result = f_write(&log0_file, flash_log0.entries[i], strlen(flash_log0.entries[i]), &bytes_written);
            if (write_result != FR_OK || bytes_written != strlen(flash_log0.entries[i])) {
                REPORT_ERROR(ERR_FILE_WRITE_FAILED, "sd_app", "Failed to write flash log0 entry");
                all_successful = 0;
                break;
            }
        }

        f_sync(&log0_file);
        f_close(&log0_file);

        if (all_successful) {
            REPORT_INFO("sd_app", "Flash log0 written to SD card successfully");
            // 成功写入后清除Flash中的log0
            clear_flash_log0();
            REPORT_INFO("sd_app", "Flash log0 cleared");
        } else {
            REPORT_ERROR(ERR_FILE_WRITE_FAILED, "sd_app", "Flash log0 write failed, keeping in flash");
        }
    } else {
        REPORT_ERROR(ERR_FILE_OPEN_FAILED, "sd_app", "Failed to create log0.txt file");
    }
}

ErrStatus memory_compare(uint8_t *src, uint8_t *dst, uint16_t length)
{
    while (length--)
    {
        if (*src++ != *dst++)
            return ERROR;
    }
    return SUCCESS;
}

void sd_fatfs_init(void)
{
    nvic_irq_enable(SDIO_IRQn, 0, 0);
}

void card_info_get(void)
{
    sd_card_info_struct sd_cardinfo;
    sd_error_enum status;
    uint32_t block_count, block_size;

    status = sd_card_information_get(&sd_cardinfo);

    if (SD_OK == status)
    {
        my_printf(DEBUG_USART, "\r\n*** SD Card Info ***\r\n");

        switch (sd_cardinfo.card_type)
        {
        case SDIO_STD_CAPACITY_SD_CARD_V1_1:
            my_printf(DEBUG_USART, "Card Type: Standard Capacity SD Card V1.1\r\n");
            break;
        case SDIO_STD_CAPACITY_SD_CARD_V2_0:
            my_printf(DEBUG_USART, "Card Type: Standard Capacity SD Card V2.0\r\n");
            break;
        case SDIO_HIGH_CAPACITY_SD_CARD:
            my_printf(DEBUG_USART, "Card Type: High Capacity SD Card\r\n");
            break;
        case SDIO_MULTIMEDIA_CARD:
            my_printf(DEBUG_USART, "Card Type: Multimedia Card\r\n");
            break;
        case SDIO_HIGH_CAPACITY_MULTIMEDIA_CARD:
            my_printf(DEBUG_USART, "Card Type: High Capacity Multimedia Card\r\n");
            break;
        case SDIO_HIGH_SPEED_MULTIMEDIA_CARD:
            my_printf(DEBUG_USART, "Card Type: High Speed Multimedia Card\r\n");
            break;
        default:
            my_printf(DEBUG_USART, "Card Type: Unknown\r\n");
            break;
        }

        block_count = (sd_cardinfo.card_csd.c_size + 1) * 1024;
        block_size = 512;
        my_printf(DEBUG_USART, "\r\n## Device size is %dKB (%.2fGB)##", sd_card_capacity_get(), sd_card_capacity_get() / 1024.0f / 1024.0f);
        my_printf(DEBUG_USART, "\r\n## Block size is %dB ##", block_size);
        my_printf(DEBUG_USART, "\r\n## Block count is %d ##", block_count);

        my_printf(DEBUG_USART, "Manufacturer ID: 0x%X\r\n", sd_cardinfo.card_cid.mid);
        my_printf(DEBUG_USART, "OEM/Application ID: 0x%X\r\n", sd_cardinfo.card_cid.oid);

        uint8_t pnm[6];
        pnm[0] = (sd_cardinfo.card_cid.pnm0 >> 24) & 0xFF;
        pnm[1] = (sd_cardinfo.card_cid.pnm0 >> 16) & 0xFF;
        pnm[2] = (sd_cardinfo.card_cid.pnm0 >> 8) & 0xFF;
        pnm[3] = sd_cardinfo.card_cid.pnm0 & 0xFF;
        pnm[4] = sd_cardinfo.card_cid.pnm1 & 0xFF;
        pnm[5] = '\0';
        my_printf(DEBUG_USART, "Product Name: %s\r\n", pnm);

        my_printf(DEBUG_USART, "Product Revision: %d.%d\r\n", (sd_cardinfo.card_cid.prv >> 4) & 0x0F, sd_cardinfo.card_cid.prv & 0x0F);

        my_printf(DEBUG_USART, "Product Serial Number: 0x%08X\r\n", sd_cardinfo.card_cid.psn);

        my_printf(DEBUG_USART, "CSD Version: %d.0\r\n", sd_cardinfo.card_csd.csd_struct + 1);
    }
    else
    {
        my_printf(DEBUG_USART, "\r\nFailed to get SD card information, error code: %d\r\n", status);
    }
}

void config_read_handler(void)
{
    FIL config_file;
    FRESULT result;
    char line_buffer[LINE_BUFFER_SIZE];

    result = f_open(&config_file, "config.ini", FA_READ);
    if (result != FR_OK)
    {
        my_printf(DEBUG_USART, "config.ini file not found.\r\n");
        return;
    }

    uint8_t ratio_section_found = 0;
    uint8_t limit_section_found = 0;
    uint8_t ratio_parsed = 0;
    uint8_t limit_parsed = 0;

    while (f_gets(line_buffer, sizeof(line_buffer), &config_file) != NULL)
    {
        char *newline = strchr(line_buffer, '\n');
        if (newline)
            *newline = '\0';
        char *carriage = strchr(line_buffer, '\r');
        if (carriage)
            *carriage = '\0';

        if (strlen(line_buffer) == 0 || line_buffer[0] == ';' || line_buffer[0] == '#')
        {
            continue;
        }

        if (line_buffer[0] == '[')
        {
            if (strstr(line_buffer, "[Ratio]") != NULL)
            {
                ratio_section_found = 1;
                limit_section_found = 0;
            }
            else if (strstr(line_buffer, "[Limit]") != NULL)
            {
                limit_section_found = 1;
                ratio_section_found = 0;
            }
            else
            {
                ratio_section_found = 0;
                limit_section_found = 0;
            }
        }
        else if (ratio_section_found && strstr(line_buffer, "Ch0") != NULL)
        {
            char *equal_sign = strchr(line_buffer, '=');
            if (equal_sign != NULL)
            {
                equal_sign++;
                while (*equal_sign == ' ')
                    equal_sign++;
                system_config.ratio_ch0 = atof(equal_sign);
                ratio_parsed = 1;
            }
        }
        else if (limit_section_found && strstr(line_buffer, "Ch0") != NULL)
        {
            char *equal_sign = strchr(line_buffer, '=');
            if (equal_sign != NULL)
            {
                equal_sign++;
                while (*equal_sign == ' ')
                    equal_sign++;
                system_config.limit_ch0 = atof(equal_sign);
                limit_parsed = 1;
            }
        }
    }

    f_close(&config_file);

    if (ratio_parsed && limit_parsed)
    {
        my_printf(DEBUG_USART, "ratio: %.1f\r\n", system_config.ratio_ch0);
        my_printf(DEBUG_USART, "limit: %.2f\r\n", system_config.limit_ch0);

        if (write_config_to_flash(&system_config) == SUCCESS)
        {
            my_printf(DEBUG_USART, "config read success\r\n");
        }
        else
        {
            //            my_printf(DEBUG_USART, "Failed to write config to flash\r\n");
        }
    }
    else
    {
        //        my_printf(DEBUG_USART, "Invalid config file format\r\n");
    }
}

void get_datetime_string(char *datetime_str)
{
    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);

    sprintf(datetime_str, "%04d%02d%02d%02d%02d%02d",
            local_time.year, local_time.month, local_time.day,
            local_time.hour, local_time.minute, local_time.second);
}

void create_storage_directories(void)
{
    f_mkdir("sample");
    f_mkdir("overLimit");
    f_mkdir("log");
    f_mkdir("hideData");
}

uint32_t get_current_log_id(void)
{
    return bsp_rtc_get_power_count();
}

void storage_init(void)
{
    FRESULT result;
    uint8_t sd_available = 0;

    // 关闭之前的所有文件句柄，确保每次启动时文件句柄状态正确
    if (log_file_open) {
        f_close(&current_log_file);
        log_file_open = 0;
        REPORT_INFO("sd_app", "Previous log file handle closed");
    }
    if (sample_file_open) {
        f_close(&current_sample_file);
        sample_file_open = 0;
        REPORT_INFO("sd_app", "Previous sample file handle closed");
    }
    if (overlimit_file_open) {
        f_close(&current_overlimit_file);
        overlimit_file_open = 0;
        REPORT_INFO("sd_app", "Previous overlimit file handle closed");
    }
    if (hidedata_file_open) {
        f_close(&current_hidedata_file);
        hidedata_file_open = 0;
        REPORT_INFO("sd_app", "Previous hidedata file handle closed");
    }

    // 重置所有文件句柄状态，确保干净的初始化环境
    memset(&current_log_file, 0, sizeof(FIL));
    memset(&current_sample_file, 0, sizeof(FIL));
    memset(&current_overlimit_file, 0, sizeof(FIL));
    memset(&current_hidedata_file, 0, sizeof(FIL));

    uint32_t current_power_count = bsp_rtc_get_power_count();

    // 尝试初始化SD卡，但不因失败而退出
    DSTATUS sd_status = disk_initialize(0);
    if (sd_status == RES_OK)
    {
        result = f_mount(0, &fs);
        if (result == FR_OK)
        {
            sd_available = 1;
        }
        else
        {
            REPORT_WARNING(ERR_SD_MOUNT_FAILED, "sd_app", "SD card mount failed");
        }
    }
    else
    {
        REPORT_WARNING(ERR_SD_INIT_FAILED, "sd_app", "SD card initialization failed");
    }

    // 只有SD卡可用时才创建目录和处理Flash日志
    if (sd_available) {
        create_storage_directories();

        // 优先检查并写入Flash中的log0日志
        if (has_flash_log0()) {
            REPORT_INFO("sd_app", "Found flash log0, writing to SD card");
            write_flash_log0_to_sd();
            REPORT_INFO("sd_app", "Flash log0 written successfully");
        } else {
            REPORT_INFO("sd_app", "No flash log0 found");
        }

        // 确保log0.txt文件存在（竞赛要求）
        ensure_log0_exists();

        // 当前启动使用当前上电次数对应的log ID
        storage_state.log_id = current_power_count;

        char log_filename[FILENAME_BUFFER_SIZE];
        sprintf(log_filename, "log/log%u.txt", storage_state.log_id);

        // 尝试打开当前power_count对应的log文件
        result = f_open(&current_log_file, log_filename, FA_OPEN_ALWAYS | FA_WRITE);
        if (result == FR_OK) {
            // 移动到文件末尾，准备追加写入
            f_lseek(&current_log_file, f_size(&current_log_file));
            log_file_open = 1;

            // 详细报告当前使用的log文件
            char info_msg[128];
            sprintf(info_msg, "Log file opened: %s (power_count=%u)", log_filename, current_power_count);
            REPORT_INFO("sd_app", info_msg);
        }
        else
        {
            char error_msg[128];
            sprintf(error_msg, "Failed to open log file: %s (error=%d)", log_filename, result);
            REPORT_ERROR(ERR_FILE_OPEN_FAILED, "sd_app", error_msg);
            log_file_open = 0;
        }
    } else {
        // SD卡不可用，设置为Flash模式（仅第一次上电时使用）
        storage_state.log_id = current_power_count;
        log_file_open = 0;
        REPORT_INFO("sd_app", "SD card not available, using Flash storage mode");
    }
}

void log_operation(const char *operation)
{
    // 安全检查：验证输入参数
    SAFETY_CHECK_NULL_PTR(operation, "sd_app");

    // 安全检查：验证字符串长度
    size_t op_len = strlen(operation);
    if (op_len == 0 || op_len > MAX_STRING_LENGTH) {
        REPORT_WARNING(ERR_INVALID_PARAMETER, "sd_app", "Invalid operation string length");
        return;
    }

    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);

    char log_entry[LOG_ENTRY_BUFFER_SIZE];
    sprintf(log_entry, "%04d-%02d-%02d %02d:%02d:%02d %s\r\n",
            local_time.year, local_time.month, local_time.day,
            local_time.hour, local_time.minute, local_time.second,
            operation);

    // 如果SD卡可用且log文件已打开，直接写入
    if (log_file_open) {
        // 文件句柄状态验证：确保current_log_file指向正确的文件
        uint32_t current_power_count = bsp_rtc_get_power_count();
        if (storage_state.log_id != current_power_count) {
            // 检测到log_id与power_count不匹配，报告异常
            char warning_msg[128];
            sprintf(warning_msg, "Log ID mismatch: storage_state.log_id=%u, power_count=%u",
                    storage_state.log_id, current_power_count);
            REPORT_WARNING(ERR_INVALID_PARAMETER, "sd_app", warning_msg);
        }

        UINT bytes_written;
        FRESULT result = f_write(&current_log_file, log_entry, strlen(log_entry), &bytes_written);
        if (result == FR_OK && bytes_written == strlen(log_entry)) {
            f_sync(&current_log_file);

            // 调试信息：记录成功写入的文件信息（仅在调试模式下）
            #ifdef DEBUG_LOG_ROUTING
            char debug_msg[128];
            sprintf(debug_msg, "Log written to log%u.txt (power_count=%u)",
                    storage_state.log_id, current_power_count);
            REPORT_INFO("sd_app", debug_msg);
            #endif
        } else {
            // 增强的错误报告：包含具体的错误信息
            char error_msg[128];
            sprintf(error_msg, "Log write failed: result=%d, bytes=%u/%u, target=log%u.txt",
                    result, bytes_written, (unsigned int)strlen(log_entry), storage_state.log_id);
            REPORT_WARNING(ERR_FILE_WRITE_FAILED, "sd_app", error_msg);
        }
    } else {
        // SD卡不可用，将日志添加到Flash log0存储（仅第一次上电时）
        uint32_t current_power_cycle = get_current_log_id();
        if (current_power_cycle == 0) {
            add_log_to_flash_log0(log_entry);

            // 调试信息：记录Flash存储操作
            #ifdef DEBUG_LOG_ROUTING
            REPORT_INFO("sd_app", "Log stored to Flash (first power cycle, no SD card)");
            #endif
        } else {
            // 调试信息：记录日志丢弃情况
            #ifdef DEBUG_LOG_ROUTING
            char debug_msg[128];
            sprintf(debug_msg, "Log discarded: power_cycle=%u, no SD card", current_power_cycle);
            REPORT_INFO("sd_app", debug_msg);
            #endif
        }
        // 如果不是第一次上电且没有SD卡，则丢弃日志
    }
}

void store_sample_data(float voltage, uint8_t over_limit)
{
    // 安全检查：验证电压值范围
    if (voltage < MIN_VOLTAGE_RANGE || voltage > MAX_VOLTAGE_RANGE) {
        REPORT_WARNING(ERR_INVALID_PARAMETER, "sd_app", "Voltage value out of reasonable range");
        return;
    }

    // 安全检查：验证超限标志
    if (over_limit > 1) {
        REPORT_WARNING(ERR_INVALID_PARAMETER, "sd_app", "Invalid over_limit flag");
        return;
    }

    if (storage_state.hide_storage_enabled)
    {
        store_hidedata(voltage, over_limit);
        return;
    }

    FRESULT result;

    if (!sample_file_open || storage_state.sample_count >= MAX_RECORDS_PER_FILE)
    {
        if (sample_file_open)
        {
            f_close(&current_sample_file);
            sample_file_open = 0;
        }

        char datetime_str[DATETIME_STRING_SIZE];
        get_datetime_string(datetime_str);
        char filename[FILENAME_BUFFER_SIZE];
        sprintf(filename, "sample/sampleData%s.txt", datetime_str);

        result = f_open(&current_sample_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
        if (result == FR_OK)
        {
            sample_file_open = 1;
            storage_state.sample_count = 0;

            // my_printf(DEBUG_USART, "Sample file created: %s\r\n", filename);
        }
        else
        {
            // my_printf(DEBUG_USART, "Failed to create sample file, error: %d\r\n", result);
            return;
        }
    }

    if (sample_file_open)
    {
        uint32_t timestamp = get_unix_timestamp();
        local_time_t local_time = timestamp_to_local_time(timestamp);

        char data_line[LINE_BUFFER_SIZE];
        sprintf(data_line, "%04d-%02d-%02d %02d:%02d:%02d %.1fV\r\n",
                local_time.year, local_time.month, local_time.day,
                local_time.hour, local_time.minute, local_time.second,
                voltage);

        UINT bytes_written;
        result = f_write(&current_sample_file, data_line, strlen(data_line), &bytes_written);
        if (result == FR_OK && bytes_written == strlen(data_line))
        {
            f_sync(&current_sample_file);
            storage_state.sample_count++;
        }
        else
        {
            // my_printf(DEBUG_USART, "Sample data write error: %d, bytes: %d/%d\r\n",
            //         result, bytes_written, strlen(data_line));
        }
    }
}

void store_overlimit_data(float voltage)
{
    // 安全检查：验证电压值范围
    if (voltage < MIN_VOLTAGE_RANGE || voltage > MAX_VOLTAGE_RANGE) {
        REPORT_WARNING(ERR_INVALID_PARAMETER, "sd_app", "Voltage value out of reasonable range");
        return;
    }

    FRESULT result;

    if (!overlimit_file_open || storage_state.overlimit_count >= MAX_RECORDS_PER_FILE)
    {
        if (overlimit_file_open)
        {
            f_close(&current_overlimit_file);
            overlimit_file_open = 0;
        }

        char datetime_str[DATETIME_STRING_SIZE];
        get_datetime_string(datetime_str);
        char filename[FILENAME_BUFFER_SIZE];
        sprintf(filename, "overLimit/overLimit%s.txt", datetime_str);

        result = f_open(&current_overlimit_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
        if (result == FR_OK)
        {
            overlimit_file_open = 1;
            storage_state.overlimit_count = 0;

            // my_printf(DEBUG_USART, "OverLimit file created: %s\r\n", filename);
        }
        else
        {
            // my_printf(DEBUG_USART, "Failed to create overlimit file, error: %d\r\n", result);
            return;
        }
    }

    if (overlimit_file_open)
    {
        uint32_t timestamp = get_unix_timestamp();
        local_time_t local_time = timestamp_to_local_time(timestamp);

        char data_line[LINE_BUFFER_SIZE];
        sprintf(data_line, "%04d-%02d-%02d %02d:%02d:%02d %.0fV limit %.0fV\r\n",
                local_time.year, local_time.month, local_time.day,
                local_time.hour, local_time.minute, local_time.second,
                voltage, system_config.limit_ch0);

        UINT bytes_written;
        result = f_write(&current_overlimit_file, data_line, strlen(data_line), &bytes_written);
        if (result == FR_OK && bytes_written == strlen(data_line))
        {
            f_sync(&current_overlimit_file);
            storage_state.overlimit_count++;
        }
        else
        {
            // my_printf(DEBUG_USART, "OverLimit data write error: %d, bytes: %d/%d\r\n",
            //          result, bytes_written, strlen(data_line));
        }
    }
}

void store_hidedata(float voltage, uint8_t over_limit)
{
    FRESULT result;

    if (!hidedata_file_open || storage_state.hidedata_count >= MAX_RECORDS_PER_FILE)
    {

        if (hidedata_file_open)
        {
            f_close(&current_hidedata_file);
            hidedata_file_open = 0;
        }

        char datetime_str[DATETIME_STRING_SIZE];
        get_datetime_string(datetime_str);
        char filename[FILENAME_BUFFER_SIZE];
        sprintf(filename, "hideData/hideData%s.txt", datetime_str);

        result = f_open(&current_hidedata_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
        if (result == FR_OK)
        {
            hidedata_file_open = 1;
            storage_state.hidedata_count = 0;
        }
        else
        {
            // my_printf(DEBUG_USART, "Failed to create hidedata file, error: %d\r\n", result);
            return;
        }
    }

    if (hidedata_file_open)
    {
        uint32_t timestamp = get_unix_timestamp();
        local_time_t local_time = timestamp_to_local_time(timestamp);

        char data_line[LOG_ENTRY_BUFFER_SIZE];

        uint32_t voltage_encrypted = voltage_to_encrypted_hex(voltage);

        sprintf(data_line, "%04d-%02d-%02d %02d:%02d:%02d %.1fV\r\nhide: %08X%08X\r\n",
                local_time.year, local_time.month, local_time.day,
                local_time.hour, local_time.minute, local_time.second,
                voltage, timestamp, voltage_encrypted);

        #ifdef HIDE_CRYPTO_DEBUG
        char debug_msg[128];
        sprintf(debug_msg, "Hide file encrypted: %.3fV -> 0x%08X", voltage, voltage_encrypted);
        REPORT_INFO("hide_crypto", debug_msg);
        #endif

        UINT bytes_written;
        result = f_write(&current_hidedata_file, data_line, strlen(data_line), &bytes_written);
        if (result == FR_OK && bytes_written == strlen(data_line))
        {
            f_sync(&current_hidedata_file);
            storage_state.hidedata_count++;
        }
        else
        {
            // my_printf(DEBUG_USART, "HideData write error: %d, bytes: %d/%d\r\n",
            //          result, bytes_written, strlen(data_line));
        }
    }
}

void save_config_to_file(void)
{
    FIL config_file;
    FRESULT result;
    char write_buffer[WRITE_BUFFER_SIZE];
    UINT bytes_written;

    result = f_open(&config_file, "config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if (result != FR_OK)
    {
        //        my_printf(DEBUG_USART, "Failed to create config.ini file: %d\r\n", result);
        return;
    }

    sprintf(write_buffer, "[Ratio]\r\nCh0=");
    result = f_write(&config_file, write_buffer, strlen(write_buffer), &bytes_written);

    sprintf(write_buffer, "%.1f\r\n\r\n[Limit]\r\nCh0=", system_config.ratio_ch0);
    result = f_write(&config_file, write_buffer, strlen(write_buffer), &bytes_written);

    sprintf(write_buffer, "%.2f\r\n", system_config.limit_ch0);
    result = f_write(&config_file, write_buffer, strlen(write_buffer), &bytes_written);

    f_close(&config_file);
}

void show_current_log_status(void)
{
    uint32_t current_power_count = bsp_rtc_get_power_count();

    my_printf(DEBUG_USART, "=== Log System Status ===\r\n");
    my_printf(DEBUG_USART, "Power count: %u\r\n", current_power_count);
    my_printf(DEBUG_USART, "Current log ID: %u\r\n", storage_state.log_id);
    my_printf(DEBUG_USART, "Expected file: log%u.txt\r\n", storage_state.log_id);
    my_printf(DEBUG_USART, "File handle open: %s\r\n", log_file_open ? "YES" : "NO");

    // 检查SD卡状态
    DSTATUS sd_status = disk_status(0);
    my_printf(DEBUG_USART, "SD card available: %s\r\n", (sd_status == RES_OK) ? "YES" : "NO");

    // 检查Flash log0状态
    my_printf(DEBUG_USART, "Flash log0 present: %s\r\n", has_flash_log0() ? "YES" : "NO");

    // 检查log_id与power_count的一致性
    if (storage_state.log_id == current_power_count) {
        my_printf(DEBUG_USART, "Log routing: CORRECT\r\n");
    } else {
        my_printf(DEBUG_USART, "Log routing: MISMATCH (log_id=%u, power_count=%u)\r\n",
                  storage_state.log_id, current_power_count);
    }

    my_printf(DEBUG_USART, "========================\r\n");
}

void show_file_handles_status(void)
{
    my_printf(DEBUG_USART, "=== File Handles Status ===\r\n");
    my_printf(DEBUG_USART, "Log file: %s\r\n", log_file_open ? "OPEN" : "CLOSED");
    my_printf(DEBUG_USART, "Sample file: %s\r\n", sample_file_open ? "OPEN" : "CLOSED");
    my_printf(DEBUG_USART, "Overlimit file: %s\r\n", overlimit_file_open ? "OPEN" : "CLOSED");
    my_printf(DEBUG_USART, "Hidedata file: %s\r\n", hidedata_file_open ? "OPEN" : "CLOSED");

    // 显示当前打开的文件信息
    if (log_file_open) {
        my_printf(DEBUG_USART, "Current log file: log%u.txt\r\n", storage_state.log_id);
    }

    my_printf(DEBUG_USART, "===========================\r\n");
}

void system_integration_test_verification(void)
{
    uint32_t current_power_count = bsp_rtc_get_power_count();
    uint32_t current_log_id = storage_state.log_id;

    my_printf(DEBUG_USART, "=== System Integration Test Verification ===\r\n");
    my_printf(DEBUG_USART, "Test Time: ");

    // 显示当前时间
    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);
    my_printf(DEBUG_USART, "%04d-%02d-%02d %02d:%02d:%02d\r\n",
              local_time.year, local_time.month, local_time.day,
              local_time.hour, local_time.minute, local_time.second);

    my_printf(DEBUG_USART, "\r\n1. Basic State Verification:\r\n");
    my_printf(DEBUG_USART, "   Power count: %u\r\n", current_power_count);
    my_printf(DEBUG_USART, "   Log ID: %u\r\n", current_log_id);

    // 验证一致性
    if (current_power_count == current_log_id) {
        my_printf(DEBUG_USART, "   Consistency: PASS (power_count == log_id)\r\n");
    } else {
        my_printf(DEBUG_USART, "   Consistency: FAIL (power_count=%u, log_id=%u)\r\n",
                  current_power_count, current_log_id);
    }

    // 验证SD卡状态
    DSTATUS sd_status = disk_status(0);
    if (sd_status == RES_OK) {
        my_printf(DEBUG_USART, "   SD Card: PASS (available)\r\n");
    } else {
        my_printf(DEBUG_USART, "   SD Card: FAIL (not available)\r\n");
        my_printf(DEBUG_USART, "\r\nTest aborted: SD card required for file verification\r\n");
        my_printf(DEBUG_USART, "============================================\r\n");
        return;
    }

    // 验证当前log文件状态
    if (log_file_open) {
        my_printf(DEBUG_USART, "   Current log file: PASS (log%u.txt open)\r\n", current_log_id);
    } else {
        my_printf(DEBUG_USART, "   Current log file: FAIL (no log file open)\r\n");
    }

    my_printf(DEBUG_USART, "\r\n2. File System Verification:\r\n");

    // 验证期望的log文件是否存在
    uint32_t files_verified = 0;
    uint32_t files_missing = 0;

    for (uint32_t i = 0; i <= current_power_count; i++) {
        char filename[64];
        sprintf(filename, "log/log%u.txt", i);

        FIL test_file;
        FRESULT result = f_open(&test_file, filename, FA_READ);
        if (result == FR_OK) {
            DWORD size = f_size(&test_file);
            f_close(&test_file);
            my_printf(DEBUG_USART, "   %s: PASS (%lu bytes)", filename, (unsigned long)size);
            if (i == current_log_id) {
                my_printf(DEBUG_USART, " [CURRENT]");
            }
            my_printf(DEBUG_USART, "\r\n");
            files_verified++;
        } else {
            my_printf(DEBUG_USART, "   %s: FAIL (missing)", filename);
            if (i == current_log_id) {
                my_printf(DEBUG_USART, " [CURRENT - CRITICAL!]");
            }
            my_printf(DEBUG_USART, "\r\n");
            files_missing++;
        }
    }

    my_printf(DEBUG_USART, "\r\n3. Test Results Summary:\r\n");
    my_printf(DEBUG_USART, "   Expected files: %u\r\n", current_power_count + 1);
    my_printf(DEBUG_USART, "   Files verified: %u\r\n", files_verified);
    my_printf(DEBUG_USART, "   Files missing: %u\r\n", files_missing);

    // 整体测试结果
    uint8_t test_passed = 1;

    if (current_power_count != current_log_id) {
        my_printf(DEBUG_USART, "   FAIL: Power count and log ID mismatch\r\n");
        test_passed = 0;
    }

    if (files_missing > 0) {
        my_printf(DEBUG_USART, "   FAIL: Missing log files detected\r\n");
        test_passed = 0;
    }

    if (!log_file_open && sd_status == RES_OK) {
        my_printf(DEBUG_USART, "   FAIL: No current log file open\r\n");
        test_passed = 0;
    }

    my_printf(DEBUG_USART, "\r\n4. Overall Test Result: ");
    if (test_passed) {
        my_printf(DEBUG_USART, "PASS\r\n");
        my_printf(DEBUG_USART, "   Log file separation mechanism is working correctly\r\n");
    } else {
        my_printf(DEBUG_USART, "FAIL\r\n");
        my_printf(DEBUG_USART, "   Issues detected in log file separation mechanism\r\n");
    }

    my_printf(DEBUG_USART, "\r\n5. Next Test Steps:\r\n");
    if (current_power_count < 3) {
        my_printf(DEBUG_USART, "   Restart system to test power_count=%u -> log%u.txt\r\n",
                  current_power_count + 1, current_power_count + 1);
    } else {
        my_printf(DEBUG_USART, "   Execute 'power reset' to restart test cycle\r\n");
    }

    my_printf(DEBUG_USART, "============================================\r\n");
}

void test_voltage_encryption_consistency(void)
{
    float test_voltages[] = {0.0f, 12.345f, 100.0f, 999.999f};
    uint8_t all_passed = 1;
    uint32_t test_count = sizeof(test_voltages) / sizeof(float);

    my_printf(DEBUG_USART, "=== Voltage Encryption Consistency Test ===\r\n");

    for (uint32_t i = 0; i < test_count; i++) {
        float original = test_voltages[i];
        uint32_t encrypted = voltage_to_encrypted_hex(original);
        float decrypted = decrypt_voltage_hex(encrypted);
        float error = (original > decrypted) ? (original - decrypted) : (decrypted - original);

        my_printf(DEBUG_USART, "Test %u: %.3fV -> 0x%08X -> %.3fV (error: %.6f)\r\n",
                  i + 1, original, encrypted, decrypted, error);

        if (error > 0.0001f) {  // 允许的精度误差
            REPORT_ERROR(ERR_INVALID_PARAMETER, "hide_test", "Encryption/decryption mismatch");
            all_passed = 0;
        }
    }

    if (all_passed) {
        my_printf(DEBUG_USART, "Result: ALL TESTS PASSED\r\n");
        REPORT_INFO("hide_test", "All encryption tests passed");
    } else {
        my_printf(DEBUG_USART, "Result: SOME TESTS FAILED\r\n");
        REPORT_ERROR(ERR_INVALID_PARAMETER, "hide_test", "Some encryption tests failed");
    }

    my_printf(DEBUG_USART, "==========================================\r\n");
}

void verify_hide_encryption_status(void)
{
    my_printf(DEBUG_USART, "=== Hide Encryption Status Verification ===\r\n");

    // 显示当前hide模式状态
    my_printf(DEBUG_USART, "Hide Mode Status:\r\n");
    my_printf(DEBUG_USART, "  Current mode: %s\r\n", sampling_state.hide_mode ? "ENABLED" : "DISABLED");
    my_printf(DEBUG_USART, "  Hide storage: %s\r\n", storage_state.hide_storage_enabled ? "ENABLED" : "DISABLED");

    // 测试当前电压值的加密一致性
    float test_voltage = adc_get_current_voltage();  // 获取当前电压值
    my_printf(DEBUG_USART, "\r\nCurrent Voltage Encryption Test:\r\n");
    my_printf(DEBUG_USART, "  Current voltage: %.3fV\r\n", test_voltage);

    uint32_t encrypted = voltage_to_encrypted_hex(test_voltage);
    float decrypted = decrypt_voltage_hex(encrypted);
    float error = (test_voltage > decrypted) ? (test_voltage - decrypted) : (decrypted - test_voltage);

    my_printf(DEBUG_USART, "  Encrypted hex: 0x%08X\r\n", encrypted);
    my_printf(DEBUG_USART, "  Decrypted voltage: %.3fV\r\n", decrypted);
    my_printf(DEBUG_USART, "  Precision error: %.6f\r\n", error);

    if (error <= 0.0001f) {
        my_printf(DEBUG_USART, "  Encryption test: PASS\r\n");
    } else {
        my_printf(DEBUG_USART, "  Encryption test: FAIL (error too large)\r\n");
        REPORT_ERROR(ERR_INVALID_PARAMETER, "hide_verify", "Encryption precision error");
    }

    my_printf(DEBUG_USART, "============================================\r\n");
}
