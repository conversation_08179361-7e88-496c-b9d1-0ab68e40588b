<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600" width="800" height="600">
  <defs>
    <linearGradient id="moduleGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="moduleGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="moduleGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b45309;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="moduleGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="moduleGradient5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b91c1c;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8fafc"/>
  
  <!-- 标题 -->
  <text x="400" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1e293b">系统功能模块</text>
  
  <!-- 中央调度器 -->
  <circle cx="400" cy="300" r="80" fill="url(#moduleGradient1)" filter="url(#shadow)"/>
  <text x="400" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">任务调度器</text>
  <text x="400" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Scheduler</text>
  <text x="400" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">5ms周期</text>
  
  <!-- ADC模块 -->
  <rect x="150" y="80" width="120" height="80" rx="12" fill="url(#moduleGradient2)" filter="url(#shadow)"/>
  <text x="210" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">ADC采样</text>
  <text x="210" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">adc_app.c</text>
  <text x="210" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">100ms周期</text>
  
  <!-- OLED模块 -->
  <rect x="530" y="80" width="120" height="80" rx="12" fill="url(#moduleGradient3)" filter="url(#shadow)"/>
  <text x="590" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">OLED显示</text>
  <text x="590" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">oled_app.c</text>
  <text x="590" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">100ms周期</text>
  
  <!-- 按键模块 -->
  <rect x="50" y="240" width="120" height="80" rx="12" fill="url(#moduleGradient4)" filter="url(#shadow)"/>
  <text x="110" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">按键处理</text>
  <text x="110" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">btn_app.c</text>
  <text x="110" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">5ms周期</text>
  
  <!-- 串口模块 -->
  <rect x="630" y="240" width="120" height="80" rx="12" fill="url(#moduleGradient5)" filter="url(#shadow)"/>
  <text x="690" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">串口通信</text>
  <text x="690" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">usart_app.c</text>
  <text x="690" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">5ms周期</text>
  
  <!-- LED模块 -->
  <rect x="150" y="440" width="120" height="80" rx="12" fill="url(#moduleGradient3)" filter="url(#shadow)"/>
  <text x="210" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">LED指示</text>
  <text x="210" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">led_app.c</text>
  <text x="210" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">50ms周期</text>
  
  <!-- 存储模块 -->
  <rect x="350" y="440" width="120" height="80" rx="12" fill="url(#moduleGradient2)" filter="url(#shadow)"/>
  <text x="410" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">存储管理</text>
  <text x="410" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">sd_app.c</text>
  <text x="410" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">数据驱动</text>
  
  <!-- RTC模块 -->
  <rect x="530" y="440" width="120" height="80" rx="12" fill="url(#moduleGradient4)" filter="url(#shadow)"/>
  <text x="590" y="470" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">时钟管理</text>
  <text x="590" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">rtc_app.c</text>
  <text x="590" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">500ms周期</text>
  
  <!-- 错误处理模块 -->
  <rect x="350" y="80" width="120" height="80" rx="12" fill="url(#moduleGradient5)" filter="url(#shadow)"/>
  <text x="410" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">错误处理</text>
  <text x="410" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">error_handler.c</text>
  <text x="410" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">事件驱动</text>
  
  <!-- 连接线 -->
  <!-- 从调度器到各模块 -->
  <line x1="340" y1="250" x2="210" y2="160" stroke="#64748b" stroke-width="2" stroke-dasharray="3,3"/>
  <line x1="460" y1="250" x2="590" y2="160" stroke="#64748b" stroke-width="2" stroke-dasharray="3,3"/>
  <line x1="320" y1="280" x2="170" y2="280" stroke="#64748b" stroke-width="2" stroke-dasharray="3,3"/>
  <line x1="480" y1="280" x2="630" y2="280" stroke="#64748b" stroke-width="2" stroke-dasharray="3,3"/>
  <line x1="340" y1="350" x2="210" y2="440" stroke="#64748b" stroke-width="2" stroke-dasharray="3,3"/>
  <line x1="400" y1="380" x2="410" y2="440" stroke="#64748b" stroke-width="2" stroke-dasharray="3,3"/>
  <line x1="460" y1="350" x2="590" y2="440" stroke="#64748b" stroke-width="2" stroke-dasharray="3,3"/>
  <line x1="400" y1="220" x2="410" y2="160" stroke="#64748b" stroke-width="2" stroke-dasharray="3,3"/>
  
  <!-- 数据流箭头 -->
  <defs>
    <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#10b981"/>
    </marker>
  </defs>
  
  <!-- ADC到存储的数据流 -->
  <path d="M 270 120 Q 340 180 350 480" stroke="#10b981" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  <text x="320" y="300" font-family="Arial, sans-serif" font-size="10" fill="#10b981" transform="rotate(-45 320 300)">数据流</text>
  
  <!-- 按键到LED的控制流 -->
  <path d="M 170 280 Q 190 360 210 440" stroke="#8b5cf6" stroke-width="3" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- 优先级标识 -->
  <rect x="50" y="550" width="700" height="40" rx="8" fill="rgba(255,255,255,0.9)" stroke="#e2e8f0" stroke-width="1"/>
  <text x="70" y="570" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1e293b">任务优先级：</text>
  <text x="170" y="570" font-family="Arial, sans-serif" font-size="11" fill="#8b5cf6">高优先级(5ms)</text>
  <text x="270" y="570" font-family="Arial, sans-serif" font-size="11" fill="#f59e0b">中优先级(50-100ms)</text>
  <text x="400" y="570" font-family="Arial, sans-serif" font-size="11" fill="#10b981">低优先级(500ms)</text>
  <text x="520" y="570" font-family="Arial, sans-serif" font-size="11" fill="#ef4444">事件驱动</text>
</svg>
