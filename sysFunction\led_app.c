#include "my_config.h"

extern sampling_state_t sampling_state;

uint8_t led_state_array[6] = {0};  // LED状态数组
uint32_t last_tick_led1 = 0;  // LED1闪烁计时器

// LED性能统计变量
static uint32_t gpio_operation_count = 0;  // GPIO操作次数统计

void bsp_led_init(void)
{
    /* 使能GPIO时钟 */
    rcu_periph_clock_enable(LED1_CLK_PORT);
    rcu_periph_clock_enable(LED_CLK_PORT);

    /* 配置LED1 (GPIOD7) */
    gpio_mode_set(LED1_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, LED1_PIN);
    gpio_output_options_set(LED1_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, LED1_PIN);

    /* 配置LED2-LED6 (GPIOB3-GPIOB7) */
    gpio_mode_set(LED_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLUP, LED2_PIN | LED3_PIN | LED4_PIN | LED5_PIN | LED6_PIN);
    gpio_output_options_set(LED_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, LED2_PIN | LED3_PIN | LED4_PIN | LED5_PIN | LED6_PIN);

    /* 初始状态为熄灭 */
    GPIO_BC(LED1_PORT) = LED1_PIN;
    GPIO_BC(LED_PORT) = LED2_PIN | LED3_PIN | LED4_PIN | LED5_PIN | LED6_PIN;
}

void led_disp(uint8_t *led_states)
{
    // 安全检查：验证输入参数
    SAFETY_CHECK_NULL_PTR(led_states, "led_app");

    // 为每个LED维护独立的状态缓存，初始化为无效状态(0xFF)
    static uint8_t last_led_state[LED_COUNT] = {0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF};

    // 逐个检查每个LED的状态变化，只操作状态改变的LED
    for (uint8_t i = 0; i < LED_COUNT; i++)
    {
        // 安全检查：数组边界检查
        SAFETY_CHECK_ARRAY_BOUNDS(i, LED_MAX_INDEX, "led_app");
        // 只有当LED状态真正改变时才操作GPIO
        if (led_states[i] != last_led_state[i])
        {
            // 更新状态缓存
            last_led_state[i] = led_states[i];

            // 根据LED索引选择对应的GPIO操作
            if (i == LED1_INDEX) {
                // LED1 (LED1_PORT LED1_PIN) - 高电平点亮，低电平熄灭
                if (led_states[LED1_INDEX]) GPIO_BOP(LED1_PORT) = LED1_PIN;
                else GPIO_BC(LED1_PORT) = LED1_PIN;
                gpio_operation_count++;  // 统计GPIO操作次数
            } else {
                // LED2-LED6 (LED_PORT LED2_PIN-LED6_PIN) - 高电平点亮，低电平熄灭
                uint32_t pin_mask = 0;
                switch (i) {
                    case LED2_INDEX: pin_mask = LED2_PIN; break;
                    case 2: pin_mask = LED3_PIN; break;
                    case 3: pin_mask = LED4_PIN; break;
                    case 4: pin_mask = LED5_PIN; break;
                    case 5: pin_mask = LED6_PIN; break;
                }

                if (led_states[i]) GPIO_BOP(LED_PORT) = pin_mask;
                else GPIO_BC(LED_PORT) = pin_mask;
                gpio_operation_count++;  // 统计GPIO操作次数
            }
        }
    }
}

void led1_sampling_indicator(void)
{
    // 安全检查：确保LED1索引在有效范围内
    if (LED1_INDEX >= LED_COUNT) {
        REPORT_ERROR(ERR_BUFFER_OVERFLOW, "led_app", "LED1 index out of bounds");
        return;
    }

    if(sampling_state.is_sampling) {
        uint32_t tick_counter = get_system_ms();

        // LED1每LED_BLINK_PERIOD_MS(500ms)切换一次状态，实现1s完整闪烁周期
        if(tick_counter >= LED_BLINK_PERIOD_MS + last_tick_led1) {
            last_tick_led1 = tick_counter;
            led_state_array[LED1_INDEX] = !led_state_array[LED1_INDEX];  // 切换LED1状态
        }
    } else {
        // 停止采样时LED1常灭
        led_state_array[LED1_INDEX] = 0;
    }
}

void led2_overlimit_set(uint8_t state)
{
    // 安全检查：验证状态参数范围（只允许0或1）
    if (state > 1) {
        REPORT_ERROR(ERR_INVALID_PARAMETER, "led_app", "Invalid LED state parameter");
        return;
    }

    // 安全检查：数组边界检查（LED2对应索引）
    if (LED2_INDEX < LED_COUNT) {
        led_state_array[LED2_INDEX] = state;  // 直接设置LED2状态
    } else {
        REPORT_ERROR(ERR_BUFFER_OVERFLOW, "led_app", "LED2 index out of bounds");
    }
}

void led_task(void)
{
    // 处理LED1采样指示
    led1_sampling_indicator();

    // 更新所有LED显示
    led_disp(led_state_array);
}

uint32_t led_get_gpio_operations(void)
{
    return gpio_operation_count;
}

void led_reset_stats(void)
{
    gpio_operation_count = 0;
}


