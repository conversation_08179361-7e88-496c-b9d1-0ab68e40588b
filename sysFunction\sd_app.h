#ifndef __SD_APP_H_
#define __SD_APP_H_

#include "stdint.h"

// SD卡基础功能
void sd_fatfs_init(void);
void sd_fatfs_test(void);
void card_info_get(void);

// 配置文件读取
void config_read_handler(void);

// 存储系统相关函数
void storage_init(void);
void create_storage_directories(void);
uint32_t get_current_log_id(void);

// Flash log0存储函数（仅在第一次上电无SD卡时使用）
void load_flash_log0(void);
void save_flash_log0(void);
void clear_flash_log0(void);
void add_log_to_flash_log0(const char *log_entry);
void write_flash_log0_to_sd(void);
uint8_t has_flash_log0(void);
void debug_flash_log0_status(void);
void ensure_log0_exists(void);
void verify_log_files_integrity(void);
void list_log_files(void);
void get_datetime_string(char* datetime_str);

// 数据存储函数
void store_sample_data(float voltage, uint8_t over_limit);
void store_overlimit_data(float voltage);
void store_hidedata(float voltage, uint8_t over_limit);
void log_operation(const char* operation);

// 配置文件存储
void save_config_to_file(void);

// 日志系统状态监控和诊断
void show_current_log_status(void);
void show_file_handles_status(void);
void system_integration_test_verification(void);

// hide功能电压加密/解密函数
uint32_t voltage_to_encrypted_hex(float voltage);
float decrypt_voltage_hex(uint32_t hex_value);
void test_voltage_encryption_consistency(void);
void verify_hide_encryption_status(void);

// 文件系统全局变量和文件句柄（外部声明）
extern uint8_t log_file_open;
extern uint8_t sample_file_open;
extern uint8_t overlimit_file_open;
extern uint8_t hidedata_file_open;

#endif /* __SD_APP_H_ */
