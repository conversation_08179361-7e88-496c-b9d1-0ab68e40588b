<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600" width="800" height="600">
  <defs>
    <linearGradient id="layerGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="layerGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="layerGradient3" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="layerGradient4" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:0.8" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f8fafc"/>
  
  <!-- 标题 -->
  <text x="400" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1e293b">GD32F470VET6 系统架构</text>
  
  <!-- 应用层 -->
  <rect x="50" y="80" width="700" height="100" rx="8" fill="url(#layerGradient1)" filter="url(#shadow)"/>
  <text x="70" y="105" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">应用层 (Application Layer)</text>
  
  <!-- 应用层组件 -->
  <rect x="80" y="120" width="80" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="120" y="143" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">main.c</text>
  
  <rect x="180" y="120" width="80" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="220" y="143" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">systick.c</text>
  
  <rect x="280" y="120" width="100" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="330" y="143" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">gd32f4xx_it.c</text>
  
  <!-- 功能层 -->
  <rect x="50" y="200" width="700" height="120" rx="8" fill="url(#layerGradient2)" filter="url(#shadow)"/>
  <text x="70" y="225" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">功能层 (Function Layer)</text>
  
  <!-- 功能层组件 -->
  <rect x="80" y="240" width="80" height="35" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="120" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">scheduler.c</text>
  
  <rect x="180" y="240" width="80" height="35" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="220" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">adc_app.c</text>
  
  <rect x="280" y="240" width="80" height="35" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="320" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">sd_app.c</text>
  
  <rect x="380" y="240" width="80" height="35" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="420" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">oled_app.c</text>
  
  <rect x="480" y="240" width="80" height="35" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="520" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">btn_app.c</text>
  
  <rect x="80" y="285" width="80" height="35" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="120" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">usart_app.c</text>
  
  <rect x="180" y="285" width="80" height="35" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="220" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">rtc_app.c</text>
  
  <rect x="280" y="285" width="80" height="35" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="320" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">led_app.c</text>
  
  <rect x="380" y="285" width="100" height="35" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="430" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">error_handler.c</text>
  
  <!-- 驱动层 -->
  <rect x="50" y="340" width="700" height="100" rx="8" fill="url(#layerGradient3)" filter="url(#shadow)"/>
  <text x="70" y="365" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">驱动层 (Driver Layer)</text>
  
  <!-- 驱动层组件 -->
  <rect x="80" y="380" width="60" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="110" y="403" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">BSP</text>
  
  <rect x="160" y="380" width="60" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="190" y="403" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">OLED</text>
  
  <rect x="240" y="380" width="80" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="280" y="403" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">GD25QXX</text>
  
  <rect x="340" y="380" width="60" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="370" y="403" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">EBTN</text>
  
  <rect x="420" y="380" width="60" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="450" y="403" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">SDIO</text>
  
  <rect x="500" y="380" width="60" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="530" y="403" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">FatFS</text>
  
  <!-- 硬件抽象层 -->
  <rect x="50" y="460" width="700" height="100" rx="8" fill="url(#layerGradient4)" filter="url(#shadow)"/>
  <text x="70" y="485" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">硬件抽象层 (HAL Layer)</text>
  
  <!-- 硬件抽象层组件 -->
  <rect x="80" y="500" width="120" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="140" y="523" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">GD32F4xx HAL</text>
  
  <rect x="220" y="500" width="80" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="260" y="523" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">CMSIS</text>
  
  <rect x="320" y="500" width="80" height="40" rx="4" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1"/>
  <text x="360" y="523" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">启动文件</text>
  
  <!-- 连接线 -->
  <line x1="400" y1="180" x2="400" y2="200" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="320" x2="400" y2="340" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="400" y1="440" x2="400" y2="460" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 箭头标记 -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b"/>
    </marker>
  </defs>
</svg>
