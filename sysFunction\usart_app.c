#include "my_config.h"
#include "sd_app.h"
#include <stdlib.h>

__IO uint16_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[512] = {0};
extern __IO uint32_t prescaler_a, prescaler_s;
extern uint16_t adc_value[1];

config_params_t system_config = {1.0f, 1.0f, 5};

sampling_state_t sampling_state = {0, 5, 0, 0};

storage_state_t storage_state = {0, 0, 0, 0, 0};

static volatile uint8_t waiting_for_ratio_input = 0;
static volatile uint8_t waiting_for_limit_input = 0;
static volatile uint8_t waiting_for_rtc_input = 0;



int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;

    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    for (tx_count = 0; tx_count < len; tx_count++)
    {
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while (RESET == usart_flag_get(usart_periph, USART_FLAG_TBE))
            ;
    }

    return len;
}

void uart_task(void)
{
    if (!rx_flag)
        return;

    char *command = (char *)uart_dma_buffer;

    int len = strlen(command);
    while (len > 0 && (command[len - 1] == '\r' || command[len - 1] == '\n' || command[len - 1] == ' '))
    {
        command[len - 1] = '\0';
        len--;
    }

    if (len > 0)
    {
        process_command(command);
    }

    memset(uart_dma_buffer, 0, 256);
    rx_flag = 0;
}

void process_command(char *command)
{
    if (waiting_for_ratio_input)
    {
        float input_value = atof(command);
        my_printf(DEBUG_USART, "%.1f\r\n", input_value);
        if (input_value >= 0.0f && input_value <= 100.0f)
        {
            system_config.ratio_ch0 = input_value;

            my_printf(DEBUG_USART, "ratio modified success\r\n");
            my_printf(DEBUG_USART, "Ratio = %.1f\r\n", system_config.ratio_ch0);

            char log_msg[64];
            sprintf(log_msg, "ratio config success to %.1f", input_value);
            log_operation(log_msg);

            save_config_to_file();
        }
        else
        {
            my_printf(DEBUG_USART, "ratio invalid\r\n");
            my_printf(DEBUG_USART, "Ratio = %.1f\r\n", system_config.ratio_ch0);
        }
        waiting_for_ratio_input = 0;
        return;
    }
    if (waiting_for_limit_input)
    {
        float input_value = atof(command);
        my_printf(DEBUG_USART, "%.2f\r\n", input_value);
        if (input_value >= 0.0f && input_value <= 200.0f)
        {
            system_config.limit_ch0 = input_value;

            my_printf(DEBUG_USART, "limit modified success\r\n");
            my_printf(DEBUG_USART, "limit = %.2f\r\n", system_config.limit_ch0);

            char log_msg[64];
            sprintf(log_msg, "limit config success to %.2f", input_value);
            log_operation(log_msg);

            save_config_to_file();
        }
        else
        {
            my_printf(DEBUG_USART, "limit invalid\r\n");
            my_printf(DEBUG_USART, "limit = %.2f\r\n", system_config.limit_ch0);
        }
        waiting_for_limit_input = 0;
        return;
    }

    if (waiting_for_rtc_input)
    {
        log_operation("rtc config");
        rtc_config_handler(command);
        waiting_for_rtc_input = 0;
        return;
    }



    if (strcmp(command, "test") == 0)
    {
        system_self_test();
    }
    else if (strcmp(command, "RTC Config") == 0)
    {
        my_printf(DEBUG_USART, "Input Datetime\r\n");
        waiting_for_rtc_input = 1;
    }
    else if (strcmp(command, "RTC now") == 0)
    {
        rtc_show_current_time();
    }
    else if (strcmp(command, "conf") == 0)
    {
        config_read_handler();
    }
    else if (strcmp(command, "ratio") == 0)
    {
        ratio_setting_handler();
    }
    else if (strcmp(command, "limit") == 0)
    {
        limit_setting_handler();
    }
    else if (strcmp(command, "config save") == 0)
    {
        config_save_handler();
    }
    else if (strcmp(command, "config read") == 0)
    {
        config_read_flash_handler();
    }
    else if (strcmp(command, "start") == 0)
    {
        sampling_start_handler();
    }
    else if (strcmp(command, "stop") == 0)
    {
        sampling_stop_handler();
    }
    else if (strcmp(command, "hide") == 0)
    {
        hide_data_handler();
    }
    else if (strcmp(command, "unhide") == 0)
    {
        unhide_data_handler();
    }
    else if (strcmp(command, "storage status") == 0)
    {
        my_printf(DEBUG_USART, "=== Storage Status ===\r\n");
        my_printf(DEBUG_USART, "Sample count: %d/10\r\n", storage_state.sample_count);
        my_printf(DEBUG_USART, "OverLimit count: %d/10\r\n", storage_state.overlimit_count);
        my_printf(DEBUG_USART, "HideData count: %d/10\r\n", storage_state.hidedata_count);
        my_printf(DEBUG_USART, "Log ID: %u\r\n", storage_state.log_id);
        my_printf(DEBUG_USART, "Hide storage: %s\r\n", storage_state.hide_storage_enabled ? "Enabled" : "Disabled");
        my_printf(DEBUG_USART, "=====================\r\n");
    }

    else if (strncmp(command, "device id", 9) == 0)
    {
        char *id_param = command + 9;
        while (*id_param == ' ')
            id_param++;
        if (*id_param != '\0')
        {
            device_id_setting_handler(id_param);
        }
        else
        {
            char current_id[32];
            read_device_id_from_flash(current_id, sizeof(current_id));
            my_printf(DEBUG_USART, "Current Device ID: %s\r\n", current_id);
            my_printf(DEBUG_USART, "Usage: device id 2025-CIMC-XXX\r\n");
        }
    }

    else if (strcmp(command, "rtc debug") == 0)
    {
        rtc_debug_info();
    }
    else if (strcmp(command, "power count") == 0)
    {
        power_count_handler();
    }
    else if (strcmp(command, "power reset") == 0)
    {
        power_count_reset_handler();
    }
    else if (strcmp(command, "log reset") == 0)
    {
        log_id_reset_handler();
    }
    else if (strncmp(command, "power set ", 10) == 0)
    {
        uint32_t new_count = atoi(command + 10);
        power_count_set_handler(new_count);
    }
    else if (strcmp(command, "flash erase") == 0)
    {
        flash_erase_handler();
    }
    else if (strcmp(command, "flash erase confirm") == 0)
    {
        flash_erase_confirm_handler();
    }
    else if (strcmp(command, "flash debug") == 0)
    {
        debug_flash_log0_status();
    }
    else if (strcmp(command, "log check") == 0)
    {
        verify_log_files_integrity();
    }
    else if (strcmp(command, "log list") == 0)
    {
        list_log_files();
    }
    else if (strcmp(command, "log fix") == 0)
    {
        ensure_log0_exists();
        my_printf(DEBUG_USART, "Log0 existence check completed\r\n");
    }
    else if (strcmp(command, "log status") == 0)
    {
        show_current_log_status();
    }
    else if (strcmp(command, "file handles") == 0)
    {
        show_file_handles_status();
    }
    else if (strcmp(command, "system status") == 0)
    {
        system_comprehensive_status();
    }
    else if (strcmp(command, "test verify") == 0)
    {
        system_integration_test_verification();
    }
    else if (strcmp(command, "hide test") == 0)
    {
        test_voltage_encryption_consistency();
    }
    else if (strcmp(command, "hide verify") == 0)
    {
        verify_hide_encryption_status();
    }


    else
    {
        my_printf(DEBUG_USART, "Unknown command: %s\r\n", command);
        my_printf(DEBUG_USART, "Available commands:\r\n");
        my_printf(DEBUG_USART, "  test - System self test\r\n");
        my_printf(DEBUG_USART, "  RTC Config - Set RTC time (interactive mode)\r\n");
        my_printf(DEBUG_USART, "  RTC now - Show current time (UTC+8)\r\n");
        my_printf(DEBUG_USART, "  conf - Read config from TF card\r\n");
        my_printf(DEBUG_USART, "  ratio - Set ratio parameter\r\n");
        my_printf(DEBUG_USART, "  limit - Set limit parameter\r\n");
        my_printf(DEBUG_USART, "  config save - Save parameters to flash\r\n");
        my_printf(DEBUG_USART, "  config read - Read parameters from flash\r\n");
        my_printf(DEBUG_USART, "  start - Start periodic sampling\r\n");
        my_printf(DEBUG_USART, "  stop - Stop periodic sampling\r\n");
        my_printf(DEBUG_USART, "\r\nHide Commands:\r\n");
        my_printf(DEBUG_USART, "  hide - Enable hide mode (encrypted data output)\r\n");
        my_printf(DEBUG_USART, "  unhide - Disable hide mode (normal data output)\r\n");
        my_printf(DEBUG_USART, "  hide test - Test voltage encryption consistency\r\n");
        my_printf(DEBUG_USART, "  hide verify - Verify current hide encryption status\r\n");
        my_printf(DEBUG_USART, "  storage status - Show storage status\r\n");
        my_printf(DEBUG_USART, "  device id [ID] - Set/Show device ID (Format: 2025-CIMC-XXX)\r\n");
        my_printf(DEBUG_USART, "  rtc debug - Show RTC debug information\r\n");
        my_printf(DEBUG_USART, "  power count - Show power-on count and system info\r\n");
        my_printf(DEBUG_USART, "  power reset - Reset power-on count to 0\r\n");
        my_printf(DEBUG_USART, "  power set [N] - Set power-on count to N (e.g., power set 0)\r\n");
        my_printf(DEBUG_USART, "  log reset - Reset log ID to 0 (next boot starts from log0)\r\n");
        my_printf(DEBUG_USART, "  flash erase - Prepare to erase entire Flash (requires confirmation)\r\n");
        my_printf(DEBUG_USART, "  flash erase confirm - Confirm and execute Flash erase (DANGEROUS!)\r\n");
        my_printf(DEBUG_USART, "  flash debug - Show Flash log0 debug information\r\n");
        my_printf(DEBUG_USART, "  log check - Check log files integrity\r\n");
        my_printf(DEBUG_USART, "  log list - List all files in log directory\r\n");
        my_printf(DEBUG_USART, "  log fix - Ensure log0.txt exists (recovery)\r\n");
        my_printf(DEBUG_USART, "  log status - Show current log system status\r\n");
        my_printf(DEBUG_USART, "  file handles - Show all file handles status\r\n");
        my_printf(DEBUG_USART, "  system status - Show comprehensive system status\r\n");
        my_printf(DEBUG_USART, "  test verify - Run integration test verification\r\n");
    }
}

void system_self_test(void)
{
    log_operation("system hardware test");

    my_printf(DEBUG_USART, "======System selftest======\r\n");

    uint32_t flash_id = spi_flash_read_id();
    if (flash_id != 0x000000 && flash_id != 0xFFFFFF)
    {
        my_printf(DEBUG_USART, "flash......ok\r\n");
    }
    else
    {
        my_printf(DEBUG_USART, "flash......error\r\n");
    }

    DSTATUS sd_status = disk_initialize(0);
    if (sd_status == 0)
    {
        my_printf(DEBUG_USART, "TF card......ok\r\n");
        my_printf(DEBUG_USART, "flash ID:0x%06X\r\n", flash_id);
        uint32_t capacity = sd_card_capacity_get();
        my_printf(DEBUG_USART, "TF card memory: %d KB\r\n", capacity);
        log_operation("test ok");
    }
    else
    {
        my_printf(DEBUG_USART, "TF card.......error\r\n");
        my_printf(DEBUG_USART, "flash ID:0x%06X\r\n", flash_id);
        my_printf(DEBUG_USART, "can not find TF card\r\n");
        log_operation("test error: tf card not found");
    }
    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);
    my_printf(DEBUG_USART, "RTC: %04d-%02d-%02d %02d:%02d:%02d\r\n",
              local_time.year, local_time.month, local_time.day,
              local_time.hour, local_time.minute, local_time.second);

    my_printf(DEBUG_USART, "=====System selftest=====\r\n\r\n");
}

void rtc_config_handler(char *time_str)
{
    int year, month, day, hour, minute, second;

    int parsed = 0;

    parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);

    if (parsed != 6)
    {
        parsed = sscanf(time_str, "%d %d %d %d %d %d", &year, &month, &day, &hour, &minute, &second);
    }

    if (parsed != 6)
    {
        parsed = sscanf(time_str, "%d/%d/%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);
    }

    if (parsed != 6)
    {
        parsed = sscanf(time_str, "%d-%d-%d %d-%d-%d", &year, &month, &day, &hour, &minute, &second);
    }

    if (parsed == 6)
    {
        if (year >= 2000 && year <= 2099 &&
            month >= 1 && month <= 12 &&
            day >= 1 && day <= 31 &&
            hour >= 0 && hour <= 23 &&
            minute >= 0 && minute <= 59 &&
            second >= 0 && second <= 59)
        {

            int utc_hour = hour - 8;
            int utc_day = day;
            int utc_month = month;
            int utc_year = year;

            if (utc_hour < 0)
            {
                utc_hour += 24;
                utc_day--;

                if (utc_day < 1)
                {
                    utc_month--;
                    if (utc_month < 1)
                    {
                        utc_month = 12;
                        utc_year--;
                    }
                    utc_day = get_days_in_month(utc_month, utc_year);
                }
            }

            extern rtc_parameter_struct rtc_initpara;

            rtc_initpara.year = dec_to_bcd(utc_year - 2000);
            rtc_initpara.month = dec_to_bcd(utc_month);
            rtc_initpara.date = dec_to_bcd(utc_day);
            rtc_initpara.day_of_week = 1;
            rtc_initpara.hour = dec_to_bcd(utc_hour);
            rtc_initpara.minute = dec_to_bcd(minute);
            rtc_initpara.second = dec_to_bcd(second);
            rtc_initpara.factor_asyn = prescaler_a;
            rtc_initpara.factor_syn = prescaler_s;
            rtc_initpara.am_pm = RTC_AM;
            if (rtc_init(&rtc_initpara) == SUCCESS)
            {
                my_printf(DEBUG_USART, "RTC Config success\r\n");
                my_printf(DEBUG_USART, "Time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
                          year, month, day, hour, minute, second);

                char log_msg[64];
                sprintf(log_msg, "rtc config success to %04d-%02d-%02d %02d:%02d:%02d",
                        year, month, day, hour, minute, second);
                log_operation(log_msg);
            }
            else
            {
                my_printf(DEBUG_USART, "Failed to set RTC time\r\n");
            }
        }
        else
        {
            my_printf(DEBUG_USART, "Invalid time parameters\r\n");
        }
    }
    else
    {
        my_printf(DEBUG_USART, "Invalid time format\r\n");
        my_printf(DEBUG_USART, "Supported formats:\r\n");
        my_printf(DEBUG_USART, "  2025-01-01 15:00:10\r\n");
        my_printf(DEBUG_USART, "  2025/01/01 15:00:10\r\n");
        my_printf(DEBUG_USART, "  2025 01 01 15 00 10\r\n");
    }
}

void rtc_show_current_time(void)
{
    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);

    my_printf(DEBUG_USART, "Current Time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
              local_time.year, local_time.month, local_time.day,
              local_time.hour, local_time.minute, local_time.second);
}

void rtc_debug_info(void)
{
    extern rtc_parameter_struct rtc_initpara;

    rtc_current_time_get(&rtc_initpara);

    my_printf(DEBUG_USART, "=== RTC Debug Info ===\r\n");

    /* RTC备份寄存器状态 */
    my_printf(DEBUG_USART, "RTC Backup Registers:\r\n");
    my_printf(DEBUG_USART, "  BKP0 (Init Flag): 0x%08X %s\r\n",
              RTC_BKP0, (RTC_BKP0 == RTC_FIRST_INIT_FLAG) ? "(Initialized)" : "(Need Init)");
    my_printf(DEBUG_USART, "  BKP1 (Power Count): %u\r\n", RTC_POWER_COUNT_BKP);
    my_printf(DEBUG_USART, "  RTC Init Status: %s\r\n", bsp_rtc_need_init() ? "Need Init" : "Initialized");

    /* RTC时间寄存器 */
    my_printf(DEBUG_USART, "Raw RTC registers (BCD format):\r\n");
    my_printf(DEBUG_USART, "  Year: 0x%02X (%d)\r\n", rtc_initpara.year, bcd_to_dec(rtc_initpara.year));
    my_printf(DEBUG_USART, "  Month: 0x%02X (%d)\r\n", rtc_initpara.month, bcd_to_dec(rtc_initpara.month));
    my_printf(DEBUG_USART, "  Date: 0x%02X (%d)\r\n", rtc_initpara.date, bcd_to_dec(rtc_initpara.date));
    my_printf(DEBUG_USART, "  Hour: 0x%02X (%d)\r\n", rtc_initpara.hour, bcd_to_dec(rtc_initpara.hour));
    my_printf(DEBUG_USART, "  Minute: 0x%02X (%d)\r\n", rtc_initpara.minute, bcd_to_dec(rtc_initpara.minute));
    my_printf(DEBUG_USART, "  Second: 0x%02X (%d)\r\n", rtc_initpara.second, bcd_to_dec(rtc_initpara.second));

    uint16_t utc_year = 2000 + bcd_to_dec(rtc_initpara.year);
    uint8_t utc_month = bcd_to_dec(rtc_initpara.month);
    uint8_t utc_day = bcd_to_dec(rtc_initpara.date);
    uint8_t utc_hour = bcd_to_dec(rtc_initpara.hour);
    uint8_t utc_minute = bcd_to_dec(rtc_initpara.minute);
    uint8_t utc_second = bcd_to_dec(rtc_initpara.second);

    my_printf(DEBUG_USART, "UTC time (stored in RTC): %04d-%02d-%02d %02d:%02d:%02d\r\n",
              utc_year, utc_month, utc_day, utc_hour, utc_minute, utc_second);

    uint32_t timestamp = get_unix_timestamp();
    my_printf(DEBUG_USART, "Unix timestamp: %u\r\n", timestamp);

    local_time_t local_time = timestamp_to_local_time(timestamp);
    my_printf(DEBUG_USART, "Local time (UTC+8): %04d-%02d-%02d %02d:%02d:%02d\r\n",
              local_time.year, local_time.month, local_time.day,
              local_time.hour, local_time.minute, local_time.second);
    my_printf(DEBUG_USART, "Timezone offset: +8 hours\r\n");

    /* 时钟源信息 */
    extern uint32_t RTCSRC_FLAG;
    my_printf(DEBUG_USART, "RTC Clock Source: ");
    switch(RTCSRC_FLAG) {
        case 0: my_printf(DEBUG_USART, "No clock\r\n"); break;
        case 1: my_printf(DEBUG_USART, "LXTAL (32.768kHz)\r\n"); break;
        case 2: my_printf(DEBUG_USART, "IRC32K (32kHz)\r\n"); break;
        case 3: my_printf(DEBUG_USART, "HXTAL/32\r\n"); break;
        default: my_printf(DEBUG_USART, "Unknown (%u)\r\n", RTCSRC_FLAG); break;
    }

    my_printf(DEBUG_USART, "=====================\r\n");
}

void print_float_manual(uint32_t usart_periph, const char *prefix, float value, const char *suffix)
{
    int integer_part = (int)value;
    int decimal_part = (int)((value - integer_part) * 100);

    if (decimal_part < 0)
        decimal_part = -decimal_part;

    my_printf(usart_periph, "%s%d.%02d%s", prefix, integer_part, decimal_part, suffix);
}

void ratio_setting_handler(void)
{

    my_printf(DEBUG_USART, "ratio= %.1f\r\n", system_config.ratio_ch0);
    my_printf(DEBUG_USART, "Input value(0~100):\r\n");

    waiting_for_ratio_input = 1;
    log_operation("ratio config");
}

void limit_setting_handler(void)
{
    my_printf(DEBUG_USART, "limit= %.2f\r\n", system_config.limit_ch0);
    my_printf(DEBUG_USART, "Input value(0~200):\r\n");

    waiting_for_limit_input = 1;
    log_operation("limit config");
}

void config_save_handler(void)
{


    my_printf(DEBUG_USART, "ratio: %.1f\r\n", system_config.ratio_ch0);
    my_printf(DEBUG_USART, "limit: %.2f\r\n", system_config.limit_ch0);

    if (write_config_to_flash(&system_config) == SUCCESS)
    {
        my_printf(DEBUG_USART, "save parameters to flash\r\n");
    }
    else
    {
        my_printf(DEBUG_USART, "Failed to save parameters to flash\r\n");
    }
}

void config_read_flash_handler(void)
{
    config_params_t temp_config;

    if (read_config_from_flash(&temp_config) == SUCCESS)
    {
        system_config = temp_config;
        my_printf(DEBUG_USART, "read parameters from flash\r\n");

        my_printf(DEBUG_USART, "ratio: %.1f\r\n", system_config.ratio_ch0);
        my_printf(DEBUG_USART, "limit: %.2f\r\n", system_config.limit_ch0);
    }
    else
    {
        my_printf(DEBUG_USART, "Failed to read parameters from flash\r\n");
    }
}

ErrStatus write_config_to_flash(config_params_t *config)
{
    uint32_t config_flash_addr = FLASH_CONFIG_ADDR;

    spi_flash_sector_erase(config_flash_addr);

    spi_flash_wait_for_write_end();

    spi_flash_buffer_write((uint8_t *)config, config_flash_addr, sizeof(config_params_t));

    config_params_t read_back_config;
    spi_flash_buffer_read((uint8_t *)&read_back_config, config_flash_addr, sizeof(config_params_t));
    if ((read_back_config.ratio_ch0 == config->ratio_ch0) &&
        (read_back_config.limit_ch0 == config->limit_ch0) &&
        (read_back_config.sample_period == config->sample_period))
    {
        return SUCCESS;
    }
    else
    {
        return ERROR;
    }
}

ErrStatus read_config_from_flash(config_params_t *config)
{
    uint32_t config_flash_addr = FLASH_CONFIG_ADDR;

    spi_flash_buffer_read((uint8_t *)config, config_flash_addr, sizeof(config_params_t));
    if (config->ratio_ch0 >= 0.0f && config->ratio_ch0 <= 100.0f &&
        config->limit_ch0 >= 0.0f && config->limit_ch0 <= 200.0f)
    {
        return SUCCESS;
    }
    else
    {
        return ERROR;
    }
}

void sampling_start_handler(void)
{
    sampling_state.is_sampling = 1;
    sampling_state.sample_period = system_config.sample_period;
    sampling_state.last_sample_time = 0;

    my_printf(DEBUG_USART, "Periodic Sampling\r\n");
    my_printf(DEBUG_USART, "sample cycle: %ds\r\n", sampling_state.sample_period);

    oled_task();

    log_operation("sample start - cycle 5s (command)");
}

void sampling_stop_handler(void)
{
    sampling_state.is_sampling = 0;

    OLED_Clear();


    led2_overlimit_set(0);
    // LED1����led1_sampling_indicator()�Զ�������ֹͣ����ʱ����

    my_printf(DEBUG_USART, "Periodic Sampling Stop\r\n");
    // my_printf(DEBUG_USART, "LED1&LED2 should be OFF now\r\n");  // ������Ϣ

    oled_task();

    log_operation("sample stop (command)");
}

void hide_data_handler(void)
{
    sampling_state.hide_mode = 1;
    storage_state.hide_storage_enabled = 1;
}

void unhide_data_handler(void)
{
    sampling_state.hide_mode = 0;
    storage_state.hide_storage_enabled = 0;
}

void print_sample_data(float voltage, uint8_t over_limit)
{
    store_sample_data(voltage, over_limit);

    if (over_limit)
    {
        store_overlimit_data(voltage);
    }

    if (sampling_state.hide_mode)
    {
        uint32_t timestamp = get_unix_timestamp();
        uint32_t voltage_hex = voltage_to_encrypted_hex(voltage);

        if (voltage_hex == 0) {
            REPORT_WARNING(ERR_INVALID_PARAMETER, "usart_app", "Voltage encryption failed");
            return;
        }

        if (over_limit)
        {
            my_printf(DEBUG_USART, "%08X%08X*\r\n", timestamp, voltage_hex);
        }
        else
        {
            my_printf(DEBUG_USART, "%08X%08X\r\n", timestamp, voltage_hex);
        }
    }
    else
    {
        uint32_t timestamp = get_unix_timestamp();
        local_time_t local_time = timestamp_to_local_time(timestamp);

        if (over_limit)
        {
            my_printf(DEBUG_USART, "%04d-%02d-%02d %02d:%02d:%02d ch0=",
                      local_time.year, local_time.month, local_time.day,
                      local_time.hour, local_time.minute, local_time.second);
            my_printf(DEBUG_USART, "%.1fV OverLimit(%.1fV)!\r\n", voltage, system_config.limit_ch0);
        }
        else
        {
            my_printf(DEBUG_USART, "%04d-%02d-%02d %02d:%02d:%02d ch0=",
                      local_time.year, local_time.month, local_time.day,
                      local_time.hour, local_time.minute, local_time.second);
            my_printf(DEBUG_USART, "%.1fV\r\n", voltage);
        }
    }
}

// 使用统一的Flash地址管理
#define DEVICE_ID_FLASH_ADDR FLASH_DEVICE_ID_ADDR  // Device ID专用区域，避免与log0冲突

#define DEFAULT_DEVICE_ID "2025-CIMC-2025710856"


void read_device_id_from_flash(char *device_id, uint16_t max_len)
{
    spi_flash_buffer_read((uint8_t *)device_id, DEVICE_ID_FLASH_ADDR, max_len);

    if (strncmp(device_id, "2025-CIMC-", 10) != 0)
    {
        strncpy(device_id, DEFAULT_DEVICE_ID, max_len - 1);
        device_id[max_len - 1] = '\0';

        spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);
        spi_flash_wait_for_write_end();
        spi_flash_buffer_write((uint8_t *)device_id, DEVICE_ID_FLASH_ADDR, strlen(device_id) + 1);
    }
}

void write_device_id_to_flash(const char *device_id)
{
    spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);
    spi_flash_wait_for_write_end();

    spi_flash_buffer_write((uint8_t *)device_id, DEVICE_ID_FLASH_ADDR, strlen(device_id) + 1);
}


void system_startup_init(void)
{
    char device_id[32];

    oled_task();

    // 从Flash中读取设备ID，如果不存在则格式化生成
    read_device_id_from_flash(device_id, sizeof(device_id));
    my_printf(DEBUG_USART, "Device_ID:%s\r\n", device_id);

    // 先初始化存储系统（会检查并写入之前的缓存日志）
    storage_init();

    // 然后记录当前系统启动日志（会写入当前的log文件）
    log_operation("system init");
}

void device_id_setting_handler(char *new_id)
{
    if (strncmp(new_id, "2025-CIMC-", 10) == 0 && strlen(new_id) <= 30)
    {
        write_device_id_to_flash(new_id);

        my_printf(DEBUG_USART, "Device ID updated: %s\r\n", new_id);
    }
    else
    {
        my_printf(DEBUG_USART, "Invalid Device ID format\r\n");
        my_printf(DEBUG_USART, "Format: 2025-CIMC-2025710856\r\n");
        my_printf(DEBUG_USART, "Example: 2025-CIMC-2025710856\r\n");
    }
}

void power_count_handler(void)
{
    uint32_t power_count = bsp_rtc_get_power_count();
    uint32_t log_id = storage_state.log_id;

    my_printf(DEBUG_USART, "=== Power Count & System Info ===\r\n");
    my_printf(DEBUG_USART, "Total power-on count: %u (from RTC backup register)\r\n", power_count);
    my_printf(DEBUG_USART, "Current log ID: %u (from storage state)\r\n", log_id);
    my_printf(DEBUG_USART, "Expected log file: log%u.txt\r\n", log_id);

    // 检查一致性
    if (power_count == log_id) {
        my_printf(DEBUG_USART, "Power count & log ID: CONSISTENT\r\n");
    } else {
        my_printf(DEBUG_USART, "Power count & log ID: MISMATCH (potential issue)\r\n");
    }

    my_printf(DEBUG_USART, "RTC init status: %s\r\n", bsp_rtc_need_init() ? "Need Init" : "Initialized");

    // 显示RTC备份寄存器状态
    my_printf(DEBUG_USART, "RTC backup registers:\r\n");
    my_printf(DEBUG_USART, "  BKP0 (Init Flag): 0x%08X %s\r\n",
              RTC_BKP0, (RTC_BKP0 == RTC_FIRST_INIT_FLAG) ? "(OK)" : "(Need Init)");
    my_printf(DEBUG_USART, "  BKP1 (Power Count): %u\r\n", RTC_POWER_COUNT_BKP);
    my_printf(DEBUG_USART, "  BKP2 (Reset Flag): 0x%08X %s\r\n",
              RTC_RESET_FLAG_BKP, (RTC_RESET_FLAG_BKP == RTC_RESET_MAGIC) ? "(Reset Pending)" : "(Normal)");

    // 显示存储状态
    my_printf(DEBUG_USART, "Storage status:\r\n");
    my_printf(DEBUG_USART, "  Log file open: %s\r\n", log_file_open ? "YES" : "NO");
    DSTATUS sd_status = disk_status(0);
    my_printf(DEBUG_USART, "  SD card status: %s\r\n", (sd_status == RES_OK) ? "AVAILABLE" : "NOT AVAILABLE");
    my_printf(DEBUG_USART, "  Flash log0 present: %s\r\n", has_flash_log0() ? "YES" : "NO");

    char device_id[32];
    read_device_id_from_flash(device_id, sizeof(device_id));
    my_printf(DEBUG_USART, "Device ID: %s\r\n", device_id);

    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);
    my_printf(DEBUG_USART, "Current time: %04d-%02d-%02d %02d:%02d:%02d (UTC+8)\r\n",
              local_time.year, local_time.month, local_time.day,
              local_time.hour, local_time.minute, local_time.second);

    my_printf(DEBUG_USART, "==================================\r\n");
}

void power_count_reset_handler(void)
{
    my_printf(DEBUG_USART, "=== Power Count Reset ===\r\n");

    uint32_t current_rtc_count = bsp_rtc_get_power_count();
    uint32_t current_log_id = storage_state.log_id;

    my_printf(DEBUG_USART, "Current RTC power-on count: %u\r\n", current_rtc_count);
    my_printf(DEBUG_USART, "Current Flash log ID: %u\r\n", current_log_id);

    /* 重置RTC备份寄存器中的上电次数 */
    bsp_rtc_reset_power_count();

    /* 清除Flash中的log0日志，重新开始记录 */
    clear_flash_log0();

    /* 当前log ID重置为0（对应上电次数0） */
    storage_state.log_id = 0;

    my_printf(DEBUG_USART, "RTC power-on count has been reset to 0\r\n");
    my_printf(DEBUG_USART, "Flash log ID has been reset to 0\r\n");
    my_printf(DEBUG_USART, "Next power-on will start from log0\r\n");

    log_operation("power count and log ID reset");

    my_printf(DEBUG_USART, "=========================\r\n");
    my_printf(DEBUG_USART, "Note: Reset will take effect immediately\r\n");
}

void log_id_reset_handler(void)
{
    my_printf(DEBUG_USART, "=== Log ID Reset ===\r\n");

    uint32_t current_log_id = storage_state.log_id;
    my_printf(DEBUG_USART, "Current log ID: %u\r\n", current_log_id);



    /* 当前log ID重置为当前上电次数 */
    storage_state.log_id = bsp_rtc_get_power_count();

    my_printf(DEBUG_USART, "Log ID has been reset to 0\r\n");
    my_printf(DEBUG_USART, "Next power-on will start from log0\r\n");
    my_printf(DEBUG_USART, "Note: RTC power-on count is not affected\r\n");

    log_operation("log ID reset");

    my_printf(DEBUG_USART, "====================\r\n");
}

void power_count_set_handler(uint32_t count)
{
    my_printf(DEBUG_USART, "=== Set Power Count ===\r\n");

    uint32_t current_count = bsp_rtc_get_power_count();
    my_printf(DEBUG_USART, "Current power-on count: %u\r\n", current_count);
    my_printf(DEBUG_USART, "Setting power-on count to: %u\r\n", count);

    /* 设置新的上电次数 */
    bsp_rtc_set_power_count(count);

    my_printf(DEBUG_USART, "Power-on count has been set to %u\r\n", count);

    if (count == 0) {
        my_printf(DEBUG_USART, "Next power-on will remain at 0\r\n");
    } else {
        my_printf(DEBUG_USART, "Next power-on will be %u\r\n", count + 1);
    }

    log_operation("power count manually set");

    my_printf(DEBUG_USART, "=======================\r\n");
}

void flash_erase_handler(void)
{
    my_printf(DEBUG_USART, "=== Flash Erase Warning ===\r\n");
    my_printf(DEBUG_USART, "WARNING: This will COMPLETELY ERASE all Flash data!\r\n");
    my_printf(DEBUG_USART, "This includes:\r\n");
    my_printf(DEBUG_USART, "  - All log files\r\n");
    my_printf(DEBUG_USART, "  - Device ID\r\n");
    my_printf(DEBUG_USART, "  - Log ID counter\r\n");
    my_printf(DEBUG_USART, "  - All stored data\r\n");
    my_printf(DEBUG_USART, "\r\n");
    my_printf(DEBUG_USART, "After erase, the system will:\r\n");
    my_printf(DEBUG_USART, "  - Generate new Device ID on next boot\r\n");
    my_printf(DEBUG_USART, "  - Reset log ID to 0\r\n");
    my_printf(DEBUG_USART, "  - Start with completely blank Flash\r\n");
    my_printf(DEBUG_USART, "\r\n");
    my_printf(DEBUG_USART, "To proceed, type: flash erase confirm\r\n");
    my_printf(DEBUG_USART, "===========================\r\n");
}

void flash_erase_confirm_handler(void)
{
    my_printf(DEBUG_USART, "=== Flash Erase Execution ===\r\n");
    my_printf(DEBUG_USART, "Starting Flash erase...\r\n");

    // 擦除外部SPI Flash（批量擦除整个芯片）
    my_printf(DEBUG_USART, "Erasing external SPI Flash (this may take several seconds)...\r\n");
    spi_flash_bulk_erase();  // 执行批量擦除
    my_printf(DEBUG_USART, "External SPI Flash erased successfully\r\n");

    // 完全重置RTC备份寄存器中的所有数据
    bsp_rtc_complete_reset();



    my_printf(DEBUG_USART, "RTC backup registers cleared\r\n");
    my_printf(DEBUG_USART, "Flash erase completed!\r\n");
    my_printf(DEBUG_USART, "\r\n");
    my_printf(DEBUG_USART, "System will restart with:\r\n");
    my_printf(DEBUG_USART, "  - Blank Flash memory\r\n");
    my_printf(DEBUG_USART, "  - New Device ID generation\r\n");
    my_printf(DEBUG_USART, "  - Log ID starting from 0\r\n");
    my_printf(DEBUG_USART, "  - RTC re-initialization\r\n");
    my_printf(DEBUG_USART, "\r\n");
    my_printf(DEBUG_USART, "Please restart the system to complete the process.\r\n");
    my_printf(DEBUG_USART, "==============================\r\n");

    log_operation("Flash completely erased");
}

void system_comprehensive_status(void)
{
    my_printf(DEBUG_USART, "=== Comprehensive System Status ===\r\n");

    // 基本系统信息
    uint32_t power_count = bsp_rtc_get_power_count();
    uint32_t log_id = storage_state.log_id;

    my_printf(DEBUG_USART, "System Info:\r\n");
    my_printf(DEBUG_USART, "  Power count: %u\r\n", power_count);
    my_printf(DEBUG_USART, "  Log ID: %u\r\n", log_id);
    my_printf(DEBUG_USART, "  Consistency: %s\r\n", (power_count == log_id) ? "OK" : "MISMATCH");

    // Device ID
    char device_id[32];
    read_device_id_from_flash(device_id, sizeof(device_id));
    my_printf(DEBUG_USART, "  Device ID: %s\r\n", device_id);

    // 时间信息
    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);
    my_printf(DEBUG_USART, "  Current time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
              local_time.year, local_time.month, local_time.day,
              local_time.hour, local_time.minute, local_time.second);

    // 存储状态
    my_printf(DEBUG_USART, "\r\nStorage Status:\r\n");
    DSTATUS sd_status = disk_status(0);
    my_printf(DEBUG_USART, "  SD card: %s\r\n", (sd_status == RES_OK) ? "AVAILABLE" : "NOT AVAILABLE");
    my_printf(DEBUG_USART, "  Flash log0: %s\r\n", has_flash_log0() ? "PRESENT" : "EMPTY");
    my_printf(DEBUG_USART, "  Expected log file: log%u.txt\r\n", log_id);

    // 文件句柄状态
    my_printf(DEBUG_USART, "\r\nFile Handles:\r\n");
    my_printf(DEBUG_USART, "  Log file: %s\r\n", log_file_open ? "OPEN" : "CLOSED");
    my_printf(DEBUG_USART, "  Sample file: %s\r\n", sample_file_open ? "OPEN" : "CLOSED");
    my_printf(DEBUG_USART, "  Overlimit file: %s\r\n", overlimit_file_open ? "OPEN" : "CLOSED");
    my_printf(DEBUG_USART, "  Hidedata file: %s\r\n", hidedata_file_open ? "OPEN" : "CLOSED");

    // RTC状态
    my_printf(DEBUG_USART, "\r\nRTC Status:\r\n");
    my_printf(DEBUG_USART, "  Init status: %s\r\n", bsp_rtc_need_init() ? "NEED INIT" : "INITIALIZED");
    my_printf(DEBUG_USART, "  BKP0 (Init): 0x%08X %s\r\n",
              RTC_BKP0, (RTC_BKP0 == RTC_FIRST_INIT_FLAG) ? "(OK)" : "(NEED INIT)");
    my_printf(DEBUG_USART, "  BKP1 (Power): %u\r\n", RTC_POWER_COUNT_BKP);
    my_printf(DEBUG_USART, "  BKP2 (Reset): 0x%08X %s\r\n",
              RTC_RESET_FLAG_BKP, (RTC_RESET_FLAG_BKP == RTC_RESET_MAGIC) ? "(RESET PENDING)" : "(NORMAL)");

    // 系统配置
    my_printf(DEBUG_USART, "\r\nSystem Config:\r\n");
    my_printf(DEBUG_USART, "  Ratio Ch0: %.1f\r\n", system_config.ratio_ch0);
    my_printf(DEBUG_USART, "  Limit Ch0: %.2f V\r\n", system_config.limit_ch0);
    my_printf(DEBUG_USART, "  Hide storage: %s\r\n", storage_state.hide_storage_enabled ? "ENABLED" : "DISABLED");

    // 健康状态评估
    my_printf(DEBUG_USART, "\r\nHealth Assessment:\r\n");
    uint8_t issues = 0;

    if (power_count != log_id) {
        my_printf(DEBUG_USART, "  WARNING: Power count and log ID mismatch\r\n");
        issues++;
    }

    if (sd_status != RES_OK) {
        my_printf(DEBUG_USART, "  WARNING: SD card not available\r\n");
        issues++;
    }

    if (bsp_rtc_need_init()) {
        my_printf(DEBUG_USART, "  WARNING: RTC needs initialization\r\n");
        issues++;
    }

    if (RTC_RESET_FLAG_BKP == RTC_RESET_MAGIC) {
        my_printf(DEBUG_USART, "  INFO: Reset flag is set (normal after reset)\r\n");
    }

    if (issues == 0) {
        my_printf(DEBUG_USART, "  Overall status: HEALTHY\r\n");
    } else {
        my_printf(DEBUG_USART, "  Overall status: %u ISSUE(S) DETECTED\r\n", issues);
    }

    my_printf(DEBUG_USART, "====================================\r\n");
}


