# 产品使用手册

## 产品概述

### 产品简介
GD32F470数据采集与存储系统是一款基于ARM Cortex-M4内核的高性能数据采集设备，专为工业监测、科研实验等应用场景设计。系统集成了高精度ADC采样、实时数据显示、双重数据存储、人机交互等功能，具有操作简便、可靠性高、扩展性强的特点。

### 主要功能
- **高精度数据采集**: 12位ADC，0.8mV精度，可配置采样周期
- **实时数据显示**: 0.91寸OLED显示屏，实时显示电压值和时间
- **双重数据存储**: SD卡主存储 + Flash备份存储，确保数据安全
- **智能人机交互**: 7个按键操作，6个LED状态指示
- **串口通信**: 115200波特率，支持参数配置和数据查询
- **实时时钟**: RTC时钟管理，精确时间戳记录

### 技术规格
| 参数项目 | 技术指标 |
|----------|----------|
| 主控芯片 | GD32F470VET6 (ARM Cortex-M4, 200MHz) |
| 存储容量 | 512KB Flash + 192KB RAM |
| ADC精度 | 12位，0.8mV@3.3V参考电压 |
| 采样频率 | 可配置1-3600秒周期 |
| 显示屏 | 0.91寸OLED，128×32像素 |
| 存储接口 | SD卡(FAT32) + SPI Flash |
| 通信接口 | USART (115200,8,N,1) |
| 工作电压 | 3.3V |
| 工作温度 | -40°C ~ +85°C |

## 硬件连接指南

### 引脚定义

#### 电源接口
```
VCC  - 3.3V电源正极
GND  - 电源地
```

#### ADC采样接口
```
PC0  - ADC通道0输入 (电压采样)
```

#### 用户交互接口
```
按键接口:
PE7  - KEY1 (采样控制)
PE8  - KEY2 (功能扩展)
PE9  - KEY3 (功能扩展)
PE10 - KEY4 (功能扩展)
PE11 - KEY5 (功能扩展)
PE12 - KEY6 (功能扩展)
PA0  - KEYW (特殊功能)

LED指示接口:
PD7  - LED1 (采样状态)
PB3  - LED2 (超限报警)
PB4  - LED3 (系统状态)
PB5  - LED4 (系统状态)
PB6  - LED5 (系统状态)
PB7  - LED6 (系统状态)
```

#### 通信接口
```
串口接口:
PA9  - USART0_TX (串口发送)
PA10 - USART0_RX (串口接收)

OLED显示接口:
PB8  - I2C0_SCL (时钟线)
PB9  - I2C0_SDA (数据线)

SPI Flash接口:
PB12 - SPI1_NSS (片选)
PB13 - SPI1_SCK (时钟)
PB14 - SPI1_MISO (主入从出)
PB15 - SPI1_MOSI (主出从入)
```

#### SD卡接口
```
SDIO接口:
PC8  - SDIO_D0
PC9  - SDIO_D1
PC10 - SDIO_D2
PC11 - SDIO_D3
PC12 - SDIO_CK
PD2  - SDIO_CMD
```

### 硬件连接图

```
                    GD32F470VET6数据采集系统
                    ┌─────────────────────────┐
    电压输入 ────────┤ PC0 (ADC)               │
                    │                         │
    串口调试 ────────┤ PA9/PA10 (USART)       │
                    │                         │
    OLED显示 ────────┤ PB8/PB9 (I2C)          │
                    │                         │
    SD卡存储 ────────┤ PC8-PC12/PD2 (SDIO)    │
                    │                         │
    按键输入 ────────┤ PE7-PE12/PA0 (GPIO)    │
                    │                         │
    LED指示  ────────┤ PD7/PB3-PB7 (GPIO)     │
                    │                         │
    电源供电 ────────┤ VCC/GND                │
                    └─────────────────────────┘
```

### 外设连接说明

#### ADC输入连接
- **输入范围**: 0-3.3V
- **输入阻抗**: >1MΩ
- **连接方式**: 直接连接或通过分压电路
- **注意事项**: 输入电压不得超过3.3V，建议加入保护电路

#### SD卡连接
- **支持格式**: FAT32文件系统
- **容量支持**: 最大32GB
- **连接方式**: 标准SDIO 4位数据线连接
- **注意事项**: 确保SD卡格式化为FAT32格式

#### 串口连接
- **连接方式**: USB转串口模块或直接连接PC串口
- **参数设置**: 115200,8,N,1
- **线序**: TX-RX交叉连接，GND共地

## 软件配置

### 系统参数配置

#### 基本参数设置
系统支持通过串口命令进行参数配置：

```
配置命令格式:
ratio <值>     - 设置ADC变比系数 (0.0-100.0)
limit <值>     - 设置超限阈值 (0.0-200.0V)
config save    - 保存配置到Flash
config read    - 从Flash读取配置
```

#### 采样参数配置
```
采样周期设置:
- 默认周期: 5秒
- 可配置范围: 1-3600秒
- 设置方法: 修改system_config.sample_period参数
```

#### 时钟配置
```
RTC时间设置:
RTC Config     - 进入时间设置模式
输入格式: 2025-01-01 15:00:10
支持格式: YYYY-MM-DD HH:MM:SS
         YYYY/MM/DD HH:MM:SS
         YYYY MM DD HH MM SS
```

### 设备ID配置
```
设备ID设置:
device id <ID> - 设置设备标识
格式要求: 2025-CIMC-XXXXXXXXXX
示例: device id 2025-CIMC-2025710856
```

## 操作指南

### 开机操作

#### 系统启动流程
1. **连接电源**: 确保3.3V电源稳定供电
2. **插入SD卡**: 插入已格式化的SD卡(FAT32格式)
3. **连接串口**: 连接串口调试线(可选)
4. **系统自检**: 观察LED指示和OLED显示
5. **初始化完成**: OLED显示"system idle"表示就绪

#### 状态指示说明
```
LED状态指示:
LED1 - 采样状态指示
  常亮: 系统空闲
  闪烁: 正在采样 (500ms周期)

LED2 - 超限报警指示
  熄灭: 正常状态
  常亮: 检测到超限

LED3-LED6 - 系统状态指示
  根据系统运行状态显示
```

```
OLED显示内容:
空闲状态: "system idle"
采样状态: 
  第一行: 当前时间 (HH:MM:SS)
  第二行: 电压值 (X.XX V)
```

### 按键操作

#### 按键功能定义
```
KEY1 - 采样控制键
  功能: 启动/停止周期采样
  操作: 短按切换采样状态

KEY2-KEY6 - 功能扩展键
  功能: 预留功能扩展
  状态: 当前版本暂未使用

KEYW - 特殊功能键
  功能: 特殊功能触发
  状态: 当前版本暂未使用
```

#### 操作步骤

**启动采样**:
1. 确保系统处于"system idle"状态
2. 按下KEY1键
3. 观察LED1开始闪烁
4. OLED显示切换为时间和电压值
5. 系统开始按设定周期进行采样

**停止采样**:
1. 在采样状态下按下KEY1键
2. 观察LED1停止闪烁，变为常亮
3. OLED显示切换回"system idle"
4. 系统停止采样

### 串口操作

#### 连接串口
1. 使用USB转串口线连接设备
2. 打开串口调试工具(如PuTTY、SecureCRT等)
3. 设置参数: 115200,8,N,1
4. 连接成功后可看到系统输出信息

#### 常用命令
```
系统测试命令:
test           - 系统硬件自检
system status  - 显示系统综合状态
storage status - 显示存储状态

采样控制命令:
start          - 启动周期采样
stop           - 停止采样

配置管理命令:
ratio          - 设置ADC变比系数
limit          - 设置超限阈值
config save    - 保存配置
config read    - 读取配置

时间管理命令:
RTC Config     - 设置RTC时间
RTC now        - 显示当前时间
power count    - 显示上电次数

数据管理命令:
log status     - 显示日志状态
file handles   - 显示文件句柄状态
hide           - 启用数据加密模式
unhide         - 禁用数据加密模式
```

### 数据查看

#### 实时数据查看
- **OLED显示**: 实时显示当前电压值和时间
- **串口输出**: 采样时自动输出数据到串口
- **LED指示**: LED2指示是否超限

#### 历史数据查看
- **SD卡文件**: 
  - sample/ 目录: 正常采样数据
  - overLimit/ 目录: 超限数据记录
  - log/ 目录: 系统日志
  - hideData/ 目录: 加密数据(如启用)

#### 数据格式说明
```
正常数据格式:
2025-01-01 15:30:25 ch0=2.45V

超限数据格式:
2025-01-01 15:30:25 ch0=3.15V OverLimit(3.00V)!

加密数据格式:
1735718425A1B2C3D4*  (时间戳+加密电压值+超限标记)
```

## 维护指导

### 日常维护

#### 定期检查项目
1. **电源电压**: 确保3.3V电源稳定
2. **SD卡状态**: 定期检查SD卡容量和文件完整性
3. **连接线路**: 检查各接口连接是否牢固
4. **环境条件**: 确保工作温度在规定范围内

#### 数据备份
1. **定期备份**: 建议每周备份SD卡数据
2. **多重备份**: 系统自动将关键数据备份到Flash
3. **数据验证**: 使用"log check"命令验证数据完整性

### 故障处理

#### 常见故障及解决方法

**故障1: 系统无法启动**
- 现象: 上电后无任何显示
- 原因: 电源问题或硬件故障
- 解决: 
  1. 检查电源电压是否为3.3V
  2. 检查电源线连接
  3. 联系技术支持

**故障2: OLED无显示**
- 现象: 系统运行但OLED黑屏
- 原因: I2C通信故障或OLED损坏
- 解决:
  1. 检查I2C连接线
  2. 使用"test"命令检查硬件状态
  3. 重启系统

**故障3: SD卡无法识别**
- 现象: 提示"TF card.......error"
- 原因: SD卡未插入、格式错误或损坏
- 解决:
  1. 确认SD卡正确插入
  2. 格式化SD卡为FAT32格式
  3. 更换SD卡测试

**故障4: ADC数据异常**
- 现象: 电压值显示异常或不变化
- 原因: ADC输入问题或配置错误
- 解决:
  1. 检查ADC输入连接
  2. 确认输入电压在0-3.3V范围
  3. 使用"test"命令检查ADC状态

**故障5: 时间显示错误**
- 现象: 时间不准确或显示异常
- 原因: RTC未初始化或时钟源问题
- 解决:
  1. 使用"RTC Config"命令重新设置时间
  2. 检查RTC备份电源
  3. 使用"rtc debug"查看详细信息

#### 系统恢复操作

**配置恢复**:
```
config read    - 从Flash读取配置
ratio 1.0      - 恢复默认变比
limit 3.0      - 恢复默认阈值
config save    - 保存默认配置
```

**时间恢复**:
```
RTC Config     - 重新设置时间
输入当前时间: 2025-01-01 15:30:00
```

**数据恢复**:
```
log fix        - 修复日志文件
log check      - 检查数据完整性
```

**系统重置**:
```
power reset    - 重置上电计数
flash erase confirm - 完全重置(慎用!)
```

### 升级更新

#### 固件升级
1. **获取固件**: 从官方渠道获取最新固件
2. **连接调试器**: 使用ST-Link或J-Link连接
3. **下载固件**: 使用Keil MDK或其他工具下载
4. **验证功能**: 升级后进行全面功能测试

#### 配置迁移
1. **备份配置**: 升级前使用"config read"备份配置
2. **记录参数**: 记录当前的ratio、limit等参数
3. **恢复配置**: 升级后重新设置参数

## 安全注意事项

### 使用限制
1. **电压限制**: ADC输入电压不得超过3.3V
2. **环境限制**: 工作温度-40°C ~ +85°C
3. **湿度限制**: 相对湿度<85%，无凝露
4. **电源限制**: 使用稳定的3.3V电源

### 安全警告
⚠️ **重要警告**:
- 输入电压超过3.3V可能损坏设备
- 请勿在易燃易爆环境中使用
- 避免设备受到强烈冲击或振动
- 请勿私自拆解或修改硬件

### 防护措施
1. **过压保护**: 建议在ADC输入端加入保护电路
2. **静电防护**: 操作时注意防静电
3. **环境保护**: 避免在恶劣环境中使用
4. **数据保护**: 定期备份重要数据

## 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **技术支持电话**: 400-XXX-XXXX
- **工作时间**: 周一至周五 9:00-18:00
- **官方网站**: www.example.com

### 常见问题FAQ

**Q1: 如何确认系统正常工作？**
A1: 使用"test"命令进行系统自检，所有项目显示"ok"表示正常。

**Q2: 如何修改采样周期？**
A2: 当前版本需要修改代码中的sample_period参数，后续版本将支持命令配置。

**Q3: SD卡支持哪些格式？**
A3: 仅支持FAT32格式，建议使用8GB-32GB的SD卡。

**Q4: 如何查看历史数据？**
A4: 将SD卡插入电脑，在相应目录下查看txt格式的数据文件。

**Q5: 系统支持多少个采样通道？**
A5: 当前版本支持1个ADC通道，硬件预留了扩展接口。

### 附录

#### 技术参数详表
| 参数类别 | 具体参数 | 数值/规格 |
|----------|----------|-----------|
| 处理器 | 型号 | GD32F470VET6 |
| 处理器 | 架构 | ARM Cortex-M4 |
| 处理器 | 主频 | 200MHz |
| 存储 | Flash | 512KB |
| 存储 | RAM | 192KB |
| ADC | 分辨率 | 12位 |
| ADC | 精度 | 0.8mV@3.3V |
| ADC | 通道数 | 1个 |
| 显示 | 类型 | OLED |
| 显示 | 尺寸 | 0.91寸 |
| 显示 | 分辨率 | 128×32像素 |
| 存储 | SD卡 | 支持FAT32 |
| 存储 | Flash | SPI接口 |
| 通信 | 串口 | 115200,8,N,1 |
| 接口 | 按键 | 7个 |
| 接口 | LED | 6个 |

#### 兼容性说明
- **SD卡兼容性**: 支持标准SD卡和microSD卡(需转接)
- **串口工具兼容性**: 支持所有标准串口调试工具
- **文件系统兼容性**: 仅支持FAT32，不支持NTFS或exFAT
- **电源兼容性**: 需要稳定的3.3V电源，不支持5V直接供电

---

**版本信息**: V1.0  
**发布日期**: 2025年1月  
**适用固件**: V1.0及以上版本  

如有疑问，请联系技术支持团队。
