#include "error_handler.h"
#include "my_config.h"
#include <string.h>

// 错误日志存储
static error_record_t error_log[ERROR_LOG_MAX_SIZE];
static uint32_t error_log_index = 0;
static uint32_t total_error_count = 0;
static bool error_log_full = false;

// 错误级别统计
static uint32_t error_count_by_level[4] = {0};

void error_handler_init(void)
{
    // 清空错误日志
    memset(error_log, 0, sizeof(error_log));
    error_log_index = 0;
    total_error_count = 0;
    error_log_full = false;
    
    // 清空错误级别统计
    memset(error_count_by_level, 0, sizeof(error_count_by_level));
    
    // 记录初始化完成
    REPORT_INFO("error_handler", "Error handler initialized");
}

const char* get_error_string(system_error_t error_code)
{
    switch (error_code) {
        case ERR_NONE: return "No error";
        
        // 存储相关错误
        case ERR_SD_INIT_FAILED: return "SD card initialization failed";
        case ERR_SD_MOUNT_FAILED: return "SD card mount failed";
        case ERR_FILE_OPEN_FAILED: return "File open failed";
        case ERR_FILE_WRITE_FAILED: return "File write failed";
        case ERR_FILE_READ_FAILED: return "File read failed";
        case ERR_FLASH_WRITE_FAILED: return "Flash write failed";
        case ERR_FLASH_READ_FAILED: return "Flash read failed";
        
        // ADC相关错误
        case ERR_ADC_TIMEOUT: return "ADC conversion timeout";
        case ERR_ADC_DATA_INVALID: return "ADC data invalid";
        case ERR_ADC_CALIBRATION_FAILED: return "ADC calibration failed";
        
        // 通信相关错误
        case ERR_OLED_COMM_FAILED: return "OLED communication failed";
        case ERR_OLED_INIT_FAILED: return "OLED initialization failed";
        case ERR_UART_TX_FAILED: return "UART transmission failed";
        case ERR_UART_RX_TIMEOUT: return "UART receive timeout";
        case ERR_I2C_BUS_ERROR: return "I2C bus error";
        case ERR_SPI_COMM_ERROR: return "SPI communication error";
        
        // 系统相关错误
        case ERR_MEMORY_ALLOCATION: return "Memory allocation failed";
        case ERR_INVALID_PARAMETER: return "Invalid parameter";
        case ERR_BUFFER_OVERFLOW: return "Buffer overflow";
        case ERR_TIMEOUT: return "Operation timeout";
        case ERR_RESOURCE_BUSY: return "Resource busy";
        case ERR_HARDWARE_FAULT: return "Hardware fault";
        
        // 配置相关错误
        case ERR_CONFIG_INVALID: return "Configuration invalid";
        case ERR_CONFIG_LOAD_FAILED: return "Configuration load failed";
        case ERR_CONFIG_SAVE_FAILED: return "Configuration save failed";
        
        default: return "Unknown error";
    }
}

void report_error(system_error_t error_code, error_level_t level, const char* module, const char* description)
{
    if (!module || !description) {
        return;  // 参数检查
    }
    
    // 更新统计信息
    total_error_count++;
    if (level < 4) {
        error_count_by_level[level]++;
    }
    
    // 记录错误到日志
    error_record_t* record = &error_log[error_log_index];
    record->error_code = error_code;
    record->level = level;
    record->timestamp = get_system_ms();
    
    // 安全复制字符串
    strncpy(record->module_name, module, ERROR_MODULE_NAME_MAX);
    record->module_name[ERROR_MODULE_NAME_MAX] = '\0';
    
    strncpy(record->description, description, ERROR_DESC_MAX);
    record->description[ERROR_DESC_MAX] = '\0';
    
    // 更新日志索引
    error_log_index = (error_log_index + 1) % ERROR_LOG_MAX_SIZE;
    if (error_log_index == 0) {
        error_log_full = true;
    }
    
    // 根据错误级别进行相应处理
    if (level >= ERR_LEVEL_ERROR) {
        // 尝试错误恢复
        attempt_recovery(error_code);
    }
}

bool attempt_recovery(system_error_t error_code)
{
    switch (error_code) {
        case ERR_SD_INIT_FAILED:
        case ERR_SD_MOUNT_FAILED:
            return recovery_sd_reinit();
            
        case ERR_OLED_COMM_FAILED:
        case ERR_OLED_INIT_FAILED:
            return recovery_oled_reinit();
            
        case ERR_ADC_CALIBRATION_FAILED:
            return recovery_adc_recalibrate();
            
        default:
            return false;  // 无恢复策略
    }
}

uint32_t get_error_count(void)
{
    return total_error_count;
}

uint32_t get_error_count_by_level(error_level_t level)
{
    if (level < 4) {
        return error_count_by_level[level];
    }
    return 0;
}

const error_record_t* get_last_error(void)
{
    if (total_error_count == 0) {
        return NULL;
    }
    
    uint32_t last_index = (error_log_index == 0) ? (ERROR_LOG_MAX_SIZE - 1) : (error_log_index - 1);
    return &error_log[last_index];
}

const error_record_t* get_error_log(uint32_t* count)
{
    if (count) {
        *count = error_log_full ? ERROR_LOG_MAX_SIZE : error_log_index;
    }
    return error_log;
}

void clear_error_log(void)
{
    memset(error_log, 0, sizeof(error_log));
    error_log_index = 0;
    total_error_count = 0;
    error_log_full = false;
    memset(error_count_by_level, 0, sizeof(error_count_by_level));
}

bool recovery_sd_reinit(void)
{
    // 尝试重新初始化SD卡
    // 这里应该调用实际的SD卡初始化函数
    // 为了保持兼容性，暂时返回false
    REPORT_INFO("recovery", "Attempting SD card recovery");
    return false;
}

bool recovery_oled_reinit(void)
{
    // 尝试重新初始化OLED
    // 这里应该调用实际的OLED初始化函数
    REPORT_INFO("recovery", "Attempting OLED recovery");
    return false;
}

bool recovery_adc_recalibrate(void)
{
    // 尝试重新校准ADC
    // 这里应该调用实际的ADC校准函数
    REPORT_INFO("recovery", "Attempting ADC recalibration");
    return false;
}
