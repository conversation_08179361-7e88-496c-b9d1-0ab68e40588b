#include "my_config.h"

extern uint16_t adc_value[1];
extern sampling_state_t sampling_state;
extern config_params_t system_config;

// OLED显示同步变量
static float last_displayed_voltage = 0.0f;  // 上次显示的电压值
static uint32_t last_displayed_time = 0;     // 上次显示的时间戳
static uint8_t voltage_updated = 0;          // 电压更新标志

int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // 临时存储格式化后的字符串
  va_list arg;      // 可变参数列表
  int len;          // 返回字符串长度

  va_start(arg, format);
  // 安全地格式化字符串到 buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}

void oled_update_voltage(float voltage, uint32_t timestamp)
{
    last_displayed_voltage = voltage;
    last_displayed_time = timestamp;
    voltage_updated = 1;  // 设置更新标志
}

void oled_task(void)
{
    static uint8_t last_sampling_state = 0;  // 记录上次采样状态

    if(!sampling_state.is_sampling) {
        // 非采样状态：显示"system idle"
        if(last_sampling_state != sampling_state.is_sampling) {
            // 状态改变时才更新显示，避免频繁刷新
            oled_printf(0, 0, "system idle     ");
            oled_printf(0, 1, "                ");  // 清空第二行
            last_sampling_state = sampling_state.is_sampling;
        }
    } else {
        // 采样状态：只在电压更新时才刷新显示
        if(voltage_updated || last_sampling_state != sampling_state.is_sampling) {
            // 使用ADC采样时的时间戳和电压值
            local_time_t local_time = timestamp_to_local_time(last_displayed_time);

            // 格式化电压值显示
            int volt_int = (int)last_displayed_voltage;
            int volt_frac = (int)((last_displayed_voltage - volt_int) * 100);

            // 更新OLED显示
            oled_printf(0, 0, "%02d:%02d:%02d   ", local_time.hour, local_time.minute, local_time.second);
            oled_printf(0, 1, "%d.%02d V   ", volt_int, volt_frac);

            // 清除更新标志
            voltage_updated = 0;
            last_sampling_state = sampling_state.is_sampling;
        }
    }
}


