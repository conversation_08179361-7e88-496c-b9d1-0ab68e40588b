#include "my_config.h"

extern uint8_t led_state_array[6];
extern sampling_state_t sampling_state;
extern config_params_t system_config;

typedef enum
{
    USER_BUTTON_0 = 0,
    USER_BUTTON_1,
    USER_BUTTON_2,
    USER_BUTTON_3,
    USER_BUTTON_4,
    USER_BUTTON_5,
    USER_BUTTON_6,
    USER_BUTTON_MAX,

} user_button_t;

void bsp_btn_init(void)
{
    /* 使能GPIO时钟 */
    rcu_periph_clock_enable(KEY_CLK_PORT);
    rcu_periph_clock_enable(KEYW_CLK_PORT);

    /* 配置KEY1-KEY6 (GPIOE7-GPIOE12) */
    gpio_mode_set(KEY_PORT, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, KEY1_PIN | KEY2_PIN | KEY3_PIN | KEY4_PIN | KEY5_PIN | KEY6_PIN);

    /* 配置KEYW (GPIOA0) */
    gpio_mode_set(KEYW_PORT, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, KEYW_PIN);
}

static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

static ebtn_btn_t btns[] = {
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_6, &defaul_ebtn_param),
};

uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
    switch (btn->key_id)
    {
    case USER_BUTTON_0:
        return !KEY1_READ;
    case USER_BUTTON_1:
        return !KEY2_READ;
    case USER_BUTTON_2:
        return !KEY3_READ;
    case USER_BUTTON_3:
        return !KEY4_READ;
    case USER_BUTTON_4:
        return !KEY5_READ;
    case USER_BUTTON_5:
        return !KEY6_READ;
    case USER_BUTTON_6:
        return !KEYW_READ;
    default:
        return 0;
    }
}

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    if (evt == EBTN_EVT_ONCLICK)
    {
        switch (btn->key_id)
        {
        case USER_BUTTON_0:
            OLED_Clear();
            
            if(sampling_state.is_sampling) {
                sampling_stop_handler();
            } else {
                sampling_start_handler();
            }
            break;
        case USER_BUTTON_1:
            system_config.sample_period = 5;
            sampling_state.sample_period = 5;
            my_printf(DEBUG_USART, "sample cycle adjust: 5s\r\n");
            write_config_to_flash(&system_config); // 持久化配置
            break;
        case USER_BUTTON_2:
            system_config.sample_period = 10;
            sampling_state.sample_period = 10;
            my_printf(DEBUG_USART, "sample cycle adjust: 10s\r\n");
            write_config_to_flash(&system_config); // 持久化配置
            break;
        case USER_BUTTON_3:
            system_config.sample_period = 15;
            sampling_state.sample_period = 15;
            my_printf(DEBUG_USART, "sample cycle adjust: 15s\r\n");
            write_config_to_flash(&system_config); // 持久化配置
            break;
        default:
            break;
        }
    }
}

void app_btn_init(void)
{

    int ret = ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);
    if(ret)
    {
        //my_printf(DEBUG_USART, "btn init success\r\n");
    }
}

void btn_task(void)
{
    ebtn_process(get_system_ms());
}
