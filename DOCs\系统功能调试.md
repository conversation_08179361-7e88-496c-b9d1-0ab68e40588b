# 系统功能调试

## 概述

本文档详细分析GD32F470VET6数据采集与存储系统的调试方法、测试策略、故障诊断和问题排查技术。通过系统性的调试工具使用、完善的测试策略和有效的故障诊断流程，为开发者提供快速定位和解决问题的实用指导。

## 调试工具分析

### 1. 串口调试系统

#### 调试接口配置
```c
// 串口调试配置
#define DEBUG_USART        USART0
#define USART_BAUDRATE     115200
#define DEBUG_BUFFER_SIZE  512

// 格式化输出函数
int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;

    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    // 逐字符发送，确保数据完整性
    for (tx_count = 0; tx_count < len; tx_count++) {
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
    }
    return len;
}
```

#### 调试命令系统
```c
// 核心调试命令
"test"              // 系统硬件自检
"system status"     // 综合系统状态
"rtc debug"         // RTC调试信息
"flash debug"       // Flash调试信息
"log status"        // 日志系统状态
"storage status"    // 存储系统状态
"file handles"      // 文件句柄状态
"power count"       // 上电次数和系统信息

// 配置调试命令
"config read"       // 读取配置参数
"config save"       // 保存配置参数
"ratio"             // 设置ADC变比系数
"limit"             // 设置阈值限制

// 功能测试命令
"start"             // 启动采样
"stop"              // 停止采样
"hide"              // 启用隐藏模式
"unhide"            // 禁用隐藏模式
"hide test"         // 测试加密一致性
"test verify"       // 集成测试验证
```

### 2. 性能计数器工具

#### perf_counter集成
```c
// 性能测量宏
#include "perf_counter.h"

// 函数执行时间测量
__cycleof__("Function Name") {
    target_function();
}

// CPU使用率监控
__cpu_usage__(sample_period) {
    main_loop();
}

// 性能统计示例
void performance_debug_example(void)
{
    // 测量调度器性能
    __cycleof__("Scheduler") {
        scheduler_run();
    }

    // 测量ADC任务性能
    __cycleof__("ADC Task") {
        adc_task();
    }

    // 测量OLED更新性能
    __cycleof__("OLED Update") {
        oled_task();
    }
}
```

### 3. Keil MDK调试器

#### 调试配置
```c
// 调试器配置要点
调试接口: ST-Link/J-Link
调试协议: SWD (Serial Wire Debug)
调试频率: 4MHz
Flash下载: 自动下载和验证
复位方式: 系统复位

// 关键调试寄存器
DWT_CYCCNT: CPU周期计数器
SysTick: 系统时钟计数器
NVIC: 中断控制器状态
RTC_BKP: RTC备份寄存器
```

#### 断点调试策略
```c
// 关键断点位置
1. main()函数入口 - 系统启动调试
2. scheduler_run() - 任务调度调试
3. adc_task() - ADC采样调试
4. error_handler() - 错误处理调试
5. 中断服务程序 - 中断响应调试

// 条件断点设置
if (error_code != ERR_NONE)     // 错误发生时断点
if (adc_value[0] > 3000)        // ADC值异常时断点
if (sampling_state.is_sampling) // 采样状态变化时断点
```

## 测试策略

### 1. 单元测试

#### 模块独立测试
```c
// ADC模块测试
void test_adc_module(void)
{
    my_printf(DEBUG_USART, "=== ADC Module Test ===\r\n");

    // 测试ADC初始化
    if (bsp_adc_init_impl() == 0) {
        my_printf(DEBUG_USART, "ADC init: PASS\r\n");
    } else {
        my_printf(DEBUG_USART, "ADC init: FAIL\r\n");
    }

    // 测试ADC数据读取
    uint16_t test_value = adc_value[0];
    if (test_value > 0 && test_value < 4096) {
        my_printf(DEBUG_USART, "ADC read: PASS (value=%d)\r\n", test_value);
    } else {
        my_printf(DEBUG_USART, "ADC read: FAIL (value=%d)\r\n", test_value);
    }

    // 测试电压计算
    float voltage = calculate_voltage(test_value);
    if (voltage >= 0.0f && voltage <= 3.3f) {
        my_printf(DEBUG_USART, "Voltage calc: PASS (%.2fV)\r\n", voltage);
    } else {
        my_printf(DEBUG_USART, "Voltage calc: FAIL (%.2fV)\r\n", voltage);
    }
}

// 存储模块测试
void test_storage_module(void)
{
    my_printf(DEBUG_USART, "=== Storage Module Test ===\r\n");

    // 测试SD卡状态
    DSTATUS sd_status = disk_status(0);
    if (sd_status == RES_OK) {
        my_printf(DEBUG_USART, "SD card: PASS\r\n");
    } else {
        my_printf(DEBUG_USART, "SD card: FAIL (status=%d)\r\n", sd_status);
    }

    // 测试Flash读写
    uint32_t flash_id = spi_flash_read_id();
    if (flash_id != 0x000000 && flash_id != 0xFFFFFF) {
        my_printf(DEBUG_USART, "Flash: PASS (ID=0x%06X)\r\n", flash_id);
    } else {
        my_printf(DEBUG_USART, "Flash: FAIL (ID=0x%06X)\r\n", flash_id);
    }
}
```

### 2. 集成测试

#### 系统硬件自检
```c
void system_self_test(void)
{
    my_printf(DEBUG_USART, "======System selftest======\r\n");

    // Flash测试
    uint32_t flash_id = spi_flash_read_id();
    if (flash_id != 0x000000 && flash_id != 0xFFFFFF) {
        my_printf(DEBUG_USART, "flash......ok\r\n");
    } else {
        my_printf(DEBUG_USART, "flash......error\r\n");
    }

    // SD卡测试
    DSTATUS sd_status = disk_initialize(0);
    if (sd_status == 0) {
        my_printf(DEBUG_USART, "TF card......ok\r\n");
        uint32_t capacity = sd_card_capacity_get();
        my_printf(DEBUG_USART, "TF card memory: %d KB\r\n", capacity);
    } else {
        my_printf(DEBUG_USART, "TF card.......error\r\n");
        my_printf(DEBUG_USART, "can not find TF card\r\n");
    }

    // RTC测试
    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);
    my_printf(DEBUG_USART, "RTC: %04d-%02d-%02d %02d:%02d:%02d\r\n",
              local_time.year, local_time.month, local_time.day,
              local_time.hour, local_time.minute, local_time.second);

    my_printf(DEBUG_USART, "=====System selftest=====\r\n");
}
```

#### 集成测试验证
```c
void system_integration_test_verification(void)
{
    my_printf(DEBUG_USART, "=== Integration Test Verification ===\r\n");

    uint8_t test_passed = 0;
    uint8_t test_total = 0;

    // 测试1: 系统初始化完整性
    test_total++;
    if (RTC_BKP0 == RTC_FIRST_INIT_FLAG) {
        my_printf(DEBUG_USART, "Test 1 - System Init: PASS\r\n");
        test_passed++;
    } else {
        my_printf(DEBUG_USART, "Test 1 - System Init: FAIL\r\n");
    }

    // 测试2: 存储系统可用性
    test_total++;
    DSTATUS sd_status = disk_status(0);
    if (sd_status == RES_OK || has_flash_log0()) {
        my_printf(DEBUG_USART, "Test 2 - Storage Available: PASS\r\n");
        test_passed++;
    } else {
        my_printf(DEBUG_USART, "Test 2 - Storage Available: FAIL\r\n");
    }

    // 测试3: 配置系统完整性
    test_total++;
    if (system_config.ratio_ch0 > 0.0f && system_config.limit_ch0 > 0.0f) {
        my_printf(DEBUG_USART, "Test 3 - Config Integrity: PASS\r\n");
        test_passed++;
    } else {
        my_printf(DEBUG_USART, "Test 3 - Config Integrity: FAIL\r\n");
    }

    // 测试4: 时钟系统功能
    test_total++;
    uint32_t timestamp = get_unix_timestamp();
    if (timestamp > 1640995200) { // 2022-01-01 00:00:00 UTC
        my_printf(DEBUG_USART, "Test 4 - RTC Function: PASS\r\n");
        test_passed++;
    } else {
        my_printf(DEBUG_USART, "Test 4 - RTC Function: FAIL\r\n");
    }

    // 测试结果汇总
    my_printf(DEBUG_USART, "\r\nTest Summary: %d/%d PASSED\r\n", test_passed, test_total);
    if (test_passed == test_total) {
        my_printf(DEBUG_USART, "Integration Test: ALL TESTS PASSED\r\n");
    } else {
        my_printf(DEBUG_USART, "Integration Test: %d TEST(S) FAILED\r\n", test_total - test_passed);
    }

    my_printf(DEBUG_USART, "=====================================\r\n");
}
```

### 3. 系统测试

#### 压力测试
```c
void system_stress_test(void)
{
    my_printf(DEBUG_USART, "=== System Stress Test ===\r\n");

    uint32_t test_duration = 60000; // 60秒压力测试
    uint32_t start_time = get_system_ms();
    uint32_t sample_count = 0;
    uint32_t error_count = 0;

    while ((get_system_ms() - start_time) < test_duration) {
        // 连续ADC采样
        if (adc_is_data_ready()) {
            sample_count++;
            float voltage = calculate_voltage(adc_value[0]);

            // 检查数据有效性
            if (voltage < 0.0f || voltage > 3.3f) {
                error_count++;
            }
        }

        // 执行调度器
        scheduler_run();

        // 每10秒输出进度
        if ((get_system_ms() - start_time) % 10000 == 0) {
            my_printf(DEBUG_USART, "Progress: %d%%, Samples: %d, Errors: %d\r\n",
                     (get_system_ms() - start_time) * 100 / test_duration,
                     sample_count, error_count);
        }
    }

    // 测试结果
    my_printf(DEBUG_USART, "Stress Test Results:\r\n");
    my_printf(DEBUG_USART, "  Duration: %d seconds\r\n", test_duration / 1000);
    my_printf(DEBUG_USART, "  Samples: %d\r\n", sample_count);
    my_printf(DEBUG_USART, "  Errors: %d\r\n", error_count);
    my_printf(DEBUG_USART, "  Error Rate: %.2f%%\r\n",
             (float)error_count * 100.0f / sample_count);

    if (error_count == 0) {
        my_printf(DEBUG_USART, "Stress Test: PASSED\r\n");
    } else {
        my_printf(DEBUG_USART, "Stress Test: FAILED\r\n");
    }

    my_printf(DEBUG_USART, "==========================\r\n");
}
```

## 故障诊断流程

### 1. 错误日志分析

#### 错误分级系统
```c
// 错误级别定义
typedef enum {
    ERR_LEVEL_INFO = 0,     // 信息级别
    ERR_LEVEL_WARNING,      // 警告级别
    ERR_LEVEL_ERROR,        // 错误级别
    ERR_LEVEL_CRITICAL      // 严重错误级别
} error_level_t;

// 错误记录结构
typedef struct {
    system_error_t error_code;                    // 错误代码
    error_level_t level;                          // 错误级别
    uint32_t timestamp;                           // 错误时间戳
    char module_name[ERROR_MODULE_NAME_MAX + 1];  // 模块名称
    char description[ERROR_DESC_MAX + 1];         // 错误描述
} error_record_t;
```

#### 错误诊断命令
```c
// 错误状态查询
void error_diagnosis_status(void)
{
    uint32_t total_errors = get_error_count();
    uint32_t critical_errors = get_error_count_by_level(ERR_LEVEL_CRITICAL);
    uint32_t error_errors = get_error_count_by_level(ERR_LEVEL_ERROR);
    uint32_t warning_errors = get_error_count_by_level(ERR_LEVEL_WARNING);

    my_printf(DEBUG_USART, "=== Error Diagnosis ===\r\n");
    my_printf(DEBUG_USART, "Total errors: %d\r\n", total_errors);
    my_printf(DEBUG_USART, "Critical: %d\r\n", critical_errors);
    my_printf(DEBUG_USART, "Error: %d\r\n", error_errors);
    my_printf(DEBUG_USART, "Warning: %d\r\n", warning_errors);

    // 显示最近的错误
    const error_record_t* last_error = get_last_error();
    if (last_error) {
        my_printf(DEBUG_USART, "Last error: %s in %s\r\n",
                 get_error_string(last_error->error_code),
                 last_error->module_name);
    }

    my_printf(DEBUG_USART, "======================\r\n");
}
```

### 2. 状态监控系统

#### 综合状态监控
```c
void system_comprehensive_status(void)
{
    my_printf(DEBUG_USART, "=== Comprehensive System Status ===\r\n");

    // 基本系统信息
    uint32_t power_count = bsp_rtc_get_power_count();
    uint32_t log_id = storage_state.log_id;

    my_printf(DEBUG_USART, "System Info:\r\n");
    my_printf(DEBUG_USART, "  Power count: %u\r\n", power_count);
    my_printf(DEBUG_USART, "  Log ID: %u\r\n", log_id);
    my_printf(DEBUG_USART, "  Consistency: %s\r\n",
             (power_count == log_id) ? "OK" : "MISMATCH");

    // 存储状态
    my_printf(DEBUG_USART, "\r\nStorage Status:\r\n");
    DSTATUS sd_status = disk_status(0);
    my_printf(DEBUG_USART, "  SD card: %s\r\n",
             (sd_status == RES_OK) ? "AVAILABLE" : "NOT AVAILABLE");
    my_printf(DEBUG_USART, "  Flash log0: %s\r\n",
             has_flash_log0() ? "PRESENT" : "EMPTY");

    // 文件句柄状态
    my_printf(DEBUG_USART, "\r\nFile Handles:\r\n");
    my_printf(DEBUG_USART, "  Log file: %s\r\n",
             log_file_open ? "OPEN" : "CLOSED");
    my_printf(DEBUG_USART, "  Sample file: %s\r\n",
             sample_file_open ? "OPEN" : "CLOSED");

    // RTC状态
    my_printf(DEBUG_USART, "\r\nRTC Status:\r\n");
    my_printf(DEBUG_USART, "  Init status: %s\r\n",
             bsp_rtc_need_init() ? "NEED INIT" : "INITIALIZED");

    // 健康状态评估
    my_printf(DEBUG_USART, "\r\nHealth Assessment:\r\n");
    uint8_t issues = 0;

    if (power_count != log_id) {
        my_printf(DEBUG_USART, "  WARNING: Power count and log ID mismatch\r\n");
        issues++;
    }

    if (sd_status != RES_OK) {
        my_printf(DEBUG_USART, "  WARNING: SD card not available\r\n");
        issues++;
    }

    if (bsp_rtc_need_init()) {
        my_printf(DEBUG_USART, "  WARNING: RTC needs initialization\r\n");
        issues++;
    }

    if (issues == 0) {
        my_printf(DEBUG_USART, "  Overall status: HEALTHY\r\n");
    } else {
        my_printf(DEBUG_USART, "  Overall status: %u ISSUE(S) DETECTED\r\n", issues);
    }

    my_printf(DEBUG_USART, "====================================\r\n");
}
```

### 3. 异常处理诊断

#### 自动恢复机制
```c
// 错误恢复流程
bool attempt_recovery(system_error_t error_code)
{
    switch (error_code) {
        case ERR_SD_INIT_FAILED:
        case ERR_SD_MOUNT_FAILED:
            return recovery_sd_reinit();

        case ERR_OLED_COMM_FAILED:
        case ERR_OLED_INIT_FAILED:
            return recovery_oled_reinit();

        case ERR_ADC_CALIBRATION_FAILED:
            return recovery_adc_recalibrate();

        default:
            return false;  // 无恢复策略
    }
}

// SD卡恢复策略
bool recovery_sd_reinit(void)
{
    my_printf(DEBUG_USART, "Attempting SD card recovery...\r\n");

    // 重新初始化SD卡
    sd_fatfs_init();

    // 验证SD卡状态
    DSTATUS status = disk_status(0);
    if (status == RES_OK) {
        my_printf(DEBUG_USART, "SD card recovery: SUCCESS\r\n");
        return true;
    } else {
        my_printf(DEBUG_USART, "SD card recovery: FAILED\r\n");
        return false;
    }
}
```

## 性能分析工具

### 1. CPU使用率监控

#### 实时性能监控
```c
// CPU使用率计算
float calculate_cpu_usage(void)
{
    static uint32_t last_idle_time = 0;
    static uint32_t last_total_time = 0;

    uint32_t current_time = get_system_ms();
    uint32_t idle_time = get_idle_time();

    uint32_t total_delta = current_time - last_total_time;
    uint32_t idle_delta = idle_time - last_idle_time;

    float cpu_usage = 0.0f;
    if (total_delta > 0) {
        cpu_usage = (1.0f - (float)idle_delta / total_delta) * 100.0f;
    }

    last_total_time = current_time;
    last_idle_time = idle_time;

    return cpu_usage;
}

// 性能统计输出
void performance_statistics(void)
{
    my_printf(DEBUG_USART, "=== Performance Statistics ===\r\n");

    // 调度器统计
    uint32_t scheduler_runs = scheduler_get_run_count();
    my_printf(DEBUG_USART, "Scheduler runs: %u\r\n", scheduler_runs);

    // 各模块统计
    uint32_t adc_samples = adc_get_sample_count();
    my_printf(DEBUG_USART, "ADC samples: %u\r\n", adc_samples);

    uint32_t gpio_operations = led_get_gpio_operations();
    my_printf(DEBUG_USART, "GPIO operations: %u\r\n", gpio_operations);

    // CPU使用率
    float cpu_usage = calculate_cpu_usage();
    my_printf(DEBUG_USART, "CPU usage: %.1f%%\r\n", cpu_usage);

    my_printf(DEBUG_USART, "==============================\r\n");
}
```

### 2. 内存使用分析

#### 内存监控工具
```c
// 内存使用统计
void memory_usage_analysis(void)
{
    my_printf(DEBUG_USART, "=== Memory Usage Analysis ===\r\n");

    // 静态内存使用
    extern uint32_t __bss_start__;
    extern uint32_t __bss_end__;
    uint32_t bss_size = &__bss_end__ - &__bss_start__;

    extern uint32_t __data_start__;
    extern uint32_t __data_end__;
    uint32_t data_size = &__data_end__ - &__data_start__;

    my_printf(DEBUG_USART, "Static Memory:\r\n");
    my_printf(DEBUG_USART, "  BSS section: %u bytes\r\n", bss_size);
    my_printf(DEBUG_USART, "  Data section: %u bytes\r\n", data_size);
    my_printf(DEBUG_USART, "  Total static: %u bytes\r\n", bss_size + data_size);

    // 栈使用分析
    uint32_t stack_used = get_stack_usage();
    my_printf(DEBUG_USART, "Stack usage: %u bytes\r\n", stack_used);

    // 堆使用分析
    uint32_t heap_used = get_heap_usage();
    my_printf(DEBUG_USART, "Heap usage: %u bytes\r\n", heap_used);

    // 缓冲区使用
    my_printf(DEBUG_USART, "Buffer Usage:\r\n");
    my_printf(DEBUG_USART, "  UART buffer: %d bytes\r\n", sizeof(rxbuffer));
    my_printf(DEBUG_USART, "  ADC buffer: %d bytes\r\n", sizeof(adc_value));
    my_printf(DEBUG_USART, "  SPI buffer: %d bytes\r\n",
             sizeof(spi1_send_array) + sizeof(spi1_receive_array));

    my_printf(DEBUG_USART, "=============================\r\n");
}
```

## 问题排查指南

### 1. 常见问题分类

#### 硬件相关问题
```c
// 硬件故障诊断流程
void hardware_diagnosis(void)
{
    my_printf(DEBUG_USART, "=== Hardware Diagnosis ===\r\n");

    // ADC问题诊断
    uint16_t adc_raw = adc_value[0];
    if (adc_raw == 0 || adc_raw == 0xFFFF) {
        my_printf(DEBUG_USART, "ADC Issue: No valid data (raw=%d)\r\n", adc_raw);
        my_printf(DEBUG_USART, "  Check: ADC power, reference voltage, input connection\r\n");
    } else if (adc_raw < 100) {
        my_printf(DEBUG_USART, "ADC Warning: Very low reading (raw=%d)\r\n", adc_raw);
        my_printf(DEBUG_USART, "  Check: Input signal level, ground connection\r\n");
    } else {
        my_printf(DEBUG_USART, "ADC Status: Normal (raw=%d)\r\n", adc_raw);
    }

    // SD卡问题诊断
    DSTATUS sd_status = disk_status(0);
    switch (sd_status) {
        case RES_OK:
            my_printf(DEBUG_USART, "SD Card: Normal\r\n");
            break;
        case STA_NOINIT:
            my_printf(DEBUG_USART, "SD Card Issue: Not initialized\r\n");
            my_printf(DEBUG_USART, "  Solution: Check card insertion, try reinit\r\n");
            break;
        case STA_NODISK:
            my_printf(DEBUG_USART, "SD Card Issue: No disk\r\n");
            my_printf(DEBUG_USART, "  Solution: Insert SD card, check connection\r\n");
            break;
        default:
            my_printf(DEBUG_USART, "SD Card Issue: Unknown error (%d)\r\n", sd_status);
            break;
    }

    // Flash问题诊断
    uint32_t flash_id = spi_flash_read_id();
    if (flash_id == 0x000000 || flash_id == 0xFFFFFF) {
        my_printf(DEBUG_USART, "Flash Issue: Communication failed (ID=0x%06X)\r\n", flash_id);
        my_printf(DEBUG_USART, "  Check: SPI connections, power supply, chip select\r\n");
    } else {
        my_printf(DEBUG_USART, "Flash Status: Normal (ID=0x%06X)\r\n", flash_id);
    }

    my_printf(DEBUG_USART, "==========================\r\n");
}
```

#### 软件相关问题
```c
// 软件故障诊断
void software_diagnosis(void)
{
    my_printf(DEBUG_USART, "=== Software Diagnosis ===\r\n");

    // 配置完整性检查
    if (system_config.ratio_ch0 <= 0.0f || system_config.ratio_ch0 > 100.0f) {
        my_printf(DEBUG_USART, "Config Issue: Invalid ratio (%.2f)\r\n",
                 system_config.ratio_ch0);
        my_printf(DEBUG_USART, "  Solution: Reset config or set valid value\r\n");
    }

    if (system_config.limit_ch0 <= 0.0f || system_config.limit_ch0 > 200.0f) {
        my_printf(DEBUG_USART, "Config Issue: Invalid limit (%.2f)\r\n",
                 system_config.limit_ch0);
        my_printf(DEBUG_USART, "  Solution: Reset config or set valid value\r\n");
    }

    // RTC状态检查
    if (bsp_rtc_need_init()) {
        my_printf(DEBUG_USART, "RTC Issue: Needs initialization\r\n");
        my_printf(DEBUG_USART, "  Solution: Set RTC time using 'RTC Config' command\r\n");
    }

    // 文件系统检查
    if (!log_file_open && disk_status(0) == RES_OK) {
        my_printf(DEBUG_USART, "File Issue: Log file not open\r\n");
        my_printf(DEBUG_USART, "  Solution: Check file system, try 'log fix' command\r\n");
    }

    // 任务调度检查
    uint32_t scheduler_runs = scheduler_get_run_count();
    if (scheduler_runs == 0) {
        my_printf(DEBUG_USART, "Scheduler Issue: Not running\r\n");
        my_printf(DEBUG_USART, "  Solution: Check main loop, system clock\r\n");
    }

    my_printf(DEBUG_USART, "==========================\r\n");
}
```

### 2. 故障排除流程图

```mermaid
graph TD
    A[系统异常] --> B{硬件还是软件?}

    B -->|硬件| C[硬件诊断]
    B -->|软件| D[软件诊断]

    C --> E{ADC问题?}
    C --> F{存储问题?}
    C --> G{通信问题?}

    E -->|是| H[检查ADC连接和配置]
    F -->|是| I[检查SD卡和Flash]
    G -->|是| J[检查串口和I2C]

    D --> K{配置问题?}
    D --> L{文件系统问题?}
    D --> M{调度问题?}

    K -->|是| N[重置或修正配置]
    L -->|是| O[修复文件系统]
    M -->|是| P[检查任务调度器]

    H --> Q[问题解决]
    I --> Q
    J --> Q
    N --> Q
    O --> Q
    P --> Q
```

### 3. 调试技巧和最佳实践

#### 断点调试技巧
```c
// 条件断点示例
void debug_breakpoint_examples(void)
{
    // 1. 错误发生时断点
    if (error_code != ERR_NONE) {
        __BKPT(1);  // 软件断点
    }

    // 2. 数据异常时断点
    if (adc_value[0] > 3900 || adc_value[0] < 100) {
        __BKPT(2);  // ADC数据异常
    }

    // 3. 状态变化时断点
    static uint8_t last_sampling_state = 0;
    if (sampling_state.is_sampling != last_sampling_state) {
        __BKPT(3);  // 采样状态变化
        last_sampling_state = sampling_state.is_sampling;
    }

    // 4. 性能监控断点
    static uint32_t last_time = 0;
    uint32_t current_time = get_system_ms();
    if (current_time - last_time > 1000) {  // 每秒触发
        __BKPT(4);  // 定时断点
        last_time = current_time;
    }
}
```

#### 变量监控策略
```c
// 关键变量监控列表
/*
实时监控变量:
1. adc_value[0]              - ADC原始值
2. sampling_state.is_sampling - 采样状态
3. system_config.ratio_ch0   - ADC变比系数
4. storage_state.log_id      - 当前日志ID
5. scheduler_run_count       - 调度器运行次数
6. total_error_count         - 错误总数

监控表达式:
1. calculate_voltage(adc_value[0])  - 计算电压值
2. get_system_ms()                  - 系统运行时间
3. disk_status(0)                   - SD卡状态
4. get_error_count()                - 错误计数

内存监控:
1. &__heap_base                     - 堆起始地址
2. &__heap_limit                    - 堆结束地址
3. &__stack_base                    - 栈起始地址
*/
```

## 测试用例设计

### 1. 功能测试用例

#### 测试用例表格
| 测试项目 | 测试步骤 | 预期结果 | 实际结果 | 状态 |
|----------|----------|----------|----------|------|
| 系统启动 | 上电启动 | 串口输出启动信息 | - | - |
| ADC采样 | 执行"test"命令 | ADC值在合理范围 | - | - |
| SD卡检测 | 执行"test"命令 | 显示"TF card......ok" | - | - |
| Flash检测 | 执行"test"命令 | 显示Flash ID | - | - |
| RTC功能 | 执行"RTC now"命令 | 显示当前时间 | - | - |
| 配置保存 | 执行"config save" | 显示保存成功 | - | - |
| 配置读取 | 执行"config read" | 显示配置参数 | - | - |
| 采样启动 | 执行"start"命令 | 开始周期采样 | - | - |
| 采样停止 | 执行"stop"命令 | 停止采样 | - | - |
| 错误处理 | 模拟错误条件 | 记录错误日志 | - | - |

### 2. 边界测试用例

#### 边界条件测试
```c
void boundary_condition_tests(void)
{
    my_printf(DEBUG_USART, "=== Boundary Condition Tests ===\r\n");

    // ADC边界测试
    uint16_t test_values[] = {0, 1, 2047, 4094, 4095, 4096};
    for (int i = 0; i < 6; i++) {
        float voltage = calculate_voltage(test_values[i]);
        my_printf(DEBUG_USART, "ADC %d -> %.3fV\r\n", test_values[i], voltage);
    }

    // 配置参数边界测试
    float test_ratios[] = {-1.0f, 0.0f, 0.1f, 50.0f, 100.0f, 101.0f};
    for (int i = 0; i < 6; i++) {
        if (test_ratios[i] >= 0.0f && test_ratios[i] <= 100.0f) {
            my_printf(DEBUG_USART, "Ratio %.1f: VALID\r\n", test_ratios[i]);
        } else {
            my_printf(DEBUG_USART, "Ratio %.1f: INVALID\r\n", test_ratios[i]);
        }
    }

    // 时间戳边界测试
    uint32_t test_timestamps[] = {0, 1640995200, 2147483647, 4294967295U};
    for (int i = 0; i < 4; i++) {
        local_time_t time = timestamp_to_local_time(test_timestamps[i]);
        my_printf(DEBUG_USART, "Timestamp %u -> %04d-%02d-%02d\r\n",
                 test_timestamps[i], time.year, time.month, time.day);
    }

    my_printf(DEBUG_USART, "================================\r\n");
}
```

## 总结

本系统的调试和测试体系提供了完整的故障诊断和问题排查解决方案，主要特点包括：

### 调试工具优势

1. **串口调试系统**: 丰富的调试命令，实时状态监控
2. **性能计数器**: 精确的性能测量和分析工具
3. **Keil调试器**: 专业的断点调试和变量监控
4. **错误处理系统**: 分级错误管理和自动恢复机制

### 测试策略特色

1. **分层测试**: 单元测试→集成测试→系统测试的完整覆盖
2. **自动化测试**: 系统自检和集成测试验证
3. **压力测试**: 长期稳定性和可靠性验证
4. **边界测试**: 极限条件下的系统行为验证

### 故障诊断能力

1. **实时监控**: 系统状态、性能指标、错误统计的实时监控
2. **智能诊断**: 自动识别常见问题并提供解决建议
3. **恢复机制**: 自动错误恢复和系统保护功能
4. **问题追踪**: 完整的错误日志和状态历史记录

该调试和测试方案为嵌入式系统开发提供了专业的调试工具和测试方法，大大提高了开发效率和系统可靠性。
```