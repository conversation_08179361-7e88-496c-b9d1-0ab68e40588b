File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
usart_app.o,15.100399%,13965,559,13786,144,35,524
c_w.l,14.181292%,13115,96,12564,551,0,96
sd_app.o,11.775392%,10890,6656,10854,16,20,6636
ff.o,8.776938%,8117,6,8098,13,6,0
sdio_sdcard.o,7.752944%,7170,68,7134,0,36,32
oled.o,4.299262%,3976,22,1242,2712,22,0
btod.o,2.326964%,2152,0,2152,0,0,0
ebtn.o,2.238298%,2070,60,2070,0,0,60
fz_wm.l,1.645743%,1522,0,1506,16,0,0
gd32f4xx_dma.o,1.440296%,1332,0,1332,0,0,0
scanf_fp.o,1.375418%,1272,0,1272,0,0,0
_printf_fp_dec.o,1.139694%,1054,0,1054,0,0,0
gd25qxx.o,1.118068%,1034,0,1034,0,0,0
my_config.o,1.085628%,1004,592,984,0,20,572
perf_counter.o,1.070490%,990,80,906,4,80,0
rtc_app.o,1.068328%,988,0,988,0,0,0
_scanf.o,0.955872%,884,0,884,0,0,0
gd32f4xx_rcu.o,0.934246%,864,0,864,0,0,0
m_wm.l,0.867205%,802,0,802,0,0,0
_printf_fp_hex.o,0.867205%,802,0,764,38,0,0
scanf_hexfp.o,0.865043%,800,0,800,0,0,0
btn_app.o,0.782863%,724,196,514,14,196,0
system_gd32f4xx.o,0.754750%,698,4,694,0,4,0
gd32f4xx_adc.o,0.707172%,654,0,654,0,0,0
gd32f4xx_usart.o,0.696359%,644,0,644,0,0,0
gd32f4xx_sdio.o,0.679058%,628,0,628,0,0,0
adc_app.o,0.612018%,566,14,552,0,14,0
led_app.o,0.573091%,530,22,508,0,22,0
error_handler.o,0.567684%,525,2841,516,0,9,2832
gd32f4xx_i2c.o,0.536326%,496,0,496,0,0,0
startup_gd32f450_470.o,0.532001%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.523351%,484,0,484,0,0,0
unicode.o,0.501725%,464,0,88,376,0,0
__printf_flags_ss_wp.o,0.442253%,409,0,392,17,0,0
oled_app.o,0.417383%,386,10,376,0,10,0
bigflt0.o,0.406570%,376,0,228,148,0,0
dmul.o,0.367643%,340,0,340,0,0,0
_scanf_int.o,0.358993%,332,0,332,0,0,0
lc_ctype_c.o,0.341692%,316,0,44,272,0,0
diskio.o,0.341692%,316,0,316,0,0,0
scanf_infnan.o,0.333041%,308,0,308,0,0,0
narrow.o,0.287627%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.283301%,262,0,262,0,0,0
main.o,0.281139%,260,0,260,0,0,0
lludivv7m.o,0.257350%,238,0,238,0,0,0
ldexp.o,0.246537%,228,0,228,0,0,0
gd32f4xx_misc.o,0.233561%,216,0,216,0,0,0
scheduler.o,0.229236%,212,104,108,0,104,0
_printf_wctomb.o,0.211935%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.203285%,188,0,148,40,0,0
_printf_intcommon.o,0.192472%,178,0,178,0,0,0
systick.o,0.181659%,168,4,164,0,4,0
gd32f4xx_it.o,0.181659%,168,0,168,0,0,0
strtod.o,0.177334%,164,0,164,0,0,0
_strtoul.o,0.170846%,158,0,158,0,0,0
dnaninf.o,0.168683%,156,0,156,0,0,0
perfc_port_default.o,0.166521%,154,0,154,0,0,0
strncmp.o,0.162195%,150,0,150,0,0,0
frexp.o,0.151382%,140,0,140,0,0,0
fnaninf.o,0.151382%,140,0,140,0,0,0
rt_memcpy_v6.o,0.149220%,138,0,138,0,0,0
lludiv10.o,0.149220%,138,0,138,0,0,0
strcmpv7m.o,0.138407%,128,0,128,0,0,0
_printf_fp_infnan.o,0.138407%,128,0,128,0,0,0
_printf_longlong_dec.o,0.134082%,124,0,124,0,0,0
dleqf.o,0.129756%,120,0,120,0,0,0
deqf.o,0.129756%,120,0,120,0,0,0
_printf_dec.o,0.129756%,120,0,120,0,0,0
strtol.o,0.121106%,112,0,112,0,0,0
_printf_oct_int_ll.o,0.121106%,112,0,112,0,0,0
drleqf.o,0.116781%,108,0,108,0,0,0
gd32f4xx_spi.o,0.112456%,104,0,104,0,0,0
retnan.o,0.108130%,100,0,100,0,0,0
rt_memcpy_w.o,0.108130%,100,0,100,0,0,0
d2f.o,0.105968%,98,0,98,0,0,0
scalbn.o,0.099480%,92,0,92,0,0,0
__dczerorl2.o,0.097317%,90,0,90,0,0,0
memcmp.o,0.095155%,88,0,88,0,0,0
f2d.o,0.092992%,86,0,86,0,0,0
strncpy.o,0.092992%,86,0,86,0,0,0
_printf_str.o,0.088667%,82,0,82,0,0,0
rt_memclr_w.o,0.084342%,78,0,78,0,0,0
_printf_pad.o,0.084342%,78,0,78,0,0,0
sys_stackheap_outer.o,0.080016%,74,0,74,0,0,0
llsdiv.o,0.077854%,72,0,72,0,0,0
lc_numeric_c.o,0.077854%,72,0,44,28,0,0
rt_memclr.o,0.073529%,68,0,68,0,0,0
dunder.o,0.069203%,64,0,64,0,0,0
_wcrtomb.o,0.069203%,64,0,64,0,0,0
_sgetc.o,0.069203%,64,0,64,0,0,0
strlen.o,0.067041%,62,0,62,0,0,0
__0sscanf.o,0.064878%,60,0,60,0,0,0
atof.o,0.060553%,56,0,56,0,0,0
vsnprintf.o,0.056228%,52,0,52,0,0,0
__scatter.o,0.056228%,52,0,52,0,0,0
fpclassify.o,0.051903%,48,0,48,0,0,0
trapv.o,0.051903%,48,0,48,0,0,0
_printf_char_common.o,0.051903%,48,0,48,0,0,0
scanf_char.o,0.047577%,44,0,44,0,0,0
_printf_wchar.o,0.047577%,44,0,44,0,0,0
_printf_char.o,0.047577%,44,0,44,0,0,0
__2sprintf.o,0.047577%,44,0,44,0,0,0
_printf_charcount.o,0.043252%,40,0,40,0,0,0
llshl.o,0.041090%,38,0,38,0,0,0
libinit2.o,0.041090%,38,0,38,0,0,0
strstr.o,0.038927%,36,0,36,0,0,0
init_aeabi.o,0.038927%,36,0,36,0,0,0
_printf_truncate.o,0.038927%,36,0,36,0,0,0
systick_wrapper_ual.o,0.034602%,32,0,32,0,0,0
_chval.o,0.030276%,28,0,28,0,0,0
__scatter_zi.o,0.030276%,28,0,28,0,0,0
atoi.o,0.028114%,26,0,26,0,0,0
dcmpi.o,0.025951%,24,0,24,0,0,0
_rserrno.o,0.023789%,22,0,22,0,0,0
strchr.o,0.021626%,20,0,20,0,0,0
gd32f4xx_pmu.o,0.021626%,20,0,20,0,0,0
isspace.o,0.019463%,18,0,18,0,0,0
exit.o,0.019463%,18,0,18,0,0,0
fpconst.o,0.017301%,16,0,0,16,0,0
dcheck1.o,0.017301%,16,0,16,0,0,0
rt_ctype_table.o,0.017301%,16,0,16,0,0,0
_snputc.o,0.017301%,16,0,16,0,0,0
__printf_wp.o,0.015138%,14,0,14,0,0,0
dretinf.o,0.012976%,12,0,12,0,0,0
sys_exit.o,0.012976%,12,0,12,0,0,0
__rtentry2.o,0.012976%,12,0,12,0,0,0
fretinf.o,0.010813%,10,0,10,0,0,0
fpinit.o,0.010813%,10,0,10,0,0,0
rtexit2.o,0.010813%,10,0,10,0,0,0
_sputc.o,0.010813%,10,0,10,0,0,0
_printf_ll.o,0.010813%,10,0,10,0,0,0
_printf_l.o,0.010813%,10,0,10,0,0,0
scanf2.o,0.008650%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.008650%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.008650%,8,0,8,0,0,0
libspace.o,0.008650%,8,96,8,0,0,96
__main.o,0.008650%,8,0,8,0,0,0
istatus.o,0.006488%,6,0,6,0,0,0
heapauxi.o,0.006488%,6,0,6,0,0,0
_printf_x.o,0.006488%,6,0,6,0,0,0
_printf_u.o,0.006488%,6,0,6,0,0,0
_printf_s.o,0.006488%,6,0,6,0,0,0
_printf_p.o,0.006488%,6,0,6,0,0,0
_printf_o.o,0.006488%,6,0,6,0,0,0
_printf_n.o,0.006488%,6,0,6,0,0,0
_printf_ls.o,0.006488%,6,0,6,0,0,0
_printf_llx.o,0.006488%,6,0,6,0,0,0
_printf_llu.o,0.006488%,6,0,6,0,0,0
_printf_llo.o,0.006488%,6,0,6,0,0,0
_printf_lli.o,0.006488%,6,0,6,0,0,0
_printf_lld.o,0.006488%,6,0,6,0,0,0
_printf_lc.o,0.006488%,6,0,6,0,0,0
_printf_i.o,0.006488%,6,0,6,0,0,0
_printf_g.o,0.006488%,6,0,6,0,0,0
_printf_f.o,0.006488%,6,0,6,0,0,0
_printf_e.o,0.006488%,6,0,6,0,0,0
_printf_d.o,0.006488%,6,0,6,0,0,0
_printf_c.o,0.006488%,6,0,6,0,0,0
_printf_a.o,0.006488%,6,0,6,0,0,0
__rtentry4.o,0.006488%,6,0,6,0,0,0
scanf1.o,0.004325%,4,0,4,0,0,0
printf2.o,0.004325%,4,0,4,0,0,0
printf1.o,0.004325%,4,0,4,0,0,0
_printf_percent_end.o,0.004325%,4,0,4,0,0,0
use_no_semi.o,0.002163%,2,0,2,0,0,0
rtexit.o,0.002163%,2,0,2,0,0,0
libshutdown2.o,0.002163%,2,0,2,0,0,0
libshutdown.o,0.002163%,2,0,2,0,0,0
libinit.o,0.002163%,2,0,2,0,0,0
