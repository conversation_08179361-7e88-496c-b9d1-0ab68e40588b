<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" width="1200" height="800">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
    </linearGradient>
    <pattern id="circuitPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="none"/>
      <circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/>
      <circle cx="80" cy="20" r="2" fill="rgba(255,255,255,0.1)"/>
      <circle cx="20" cy="80" r="2" fill="rgba(255,255,255,0.1)"/>
      <circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/>
      <line x1="20" y1="20" x2="80" y2="20" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
      <line x1="20" y1="20" x2="20" y2="80" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
      <line x1="80" y1="20" x2="80" y2="80" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
      <line x1="20" y1="80" x2="80" y2="80" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
    </pattern>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景渐变 -->
  <rect width="1200" height="800" fill="url(#bgGradient)"/>
  
  <!-- 电路板图案 -->
  <rect width="1200" height="800" fill="url(#circuitPattern)" opacity="0.3"/>
  
  <!-- 浮动的芯片图标 -->
  <g opacity="0.2">
    <!-- 大芯片 -->
    <rect x="100" y="150" width="60" height="60" rx="8" fill="rgba(255,255,255,0.3)" stroke="rgba(255,255,255,0.5)" stroke-width="2"/>
    <circle cx="115" cy="165" r="3" fill="rgba(245,158,11,0.8)"/>
    <circle cx="130" cy="165" r="3" fill="rgba(245,158,11,0.8)"/>
    <circle cx="145" cy="165" r="3" fill="rgba(245,158,11,0.8)"/>
    <circle cx="115" cy="180" r="3" fill="rgba(245,158,11,0.8)"/>
    <circle cx="130" cy="180" r="3" fill="rgba(245,158,11,0.8)"/>
    <circle cx="145" cy="180" r="3" fill="rgba(245,158,11,0.8)"/>
    <circle cx="115" cy="195" r="3" fill="rgba(245,158,11,0.8)"/>
    <circle cx="130" cy="195" r="3" fill="rgba(245,158,11,0.8)"/>
    <circle cx="145" cy="195" r="3" fill="rgba(245,158,11,0.8)"/>
    
    <!-- 中芯片 -->
    <rect x="900" y="300" width="40" height="40" rx="6" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="1"/>
    <circle cx="910" cy="310" r="2" fill="rgba(245,158,11,0.6)"/>
    <circle cx="920" cy="310" r="2" fill="rgba(245,158,11,0.6)"/>
    <circle cx="930" cy="310" r="2" fill="rgba(245,158,11,0.6)"/>
    <circle cx="910" cy="320" r="2" fill="rgba(245,158,11,0.6)"/>
    <circle cx="920" cy="320" r="2" fill="rgba(245,158,11,0.6)"/>
    <circle cx="930" cy="320" r="2" fill="rgba(245,158,11,0.6)"/>
    <circle cx="910" cy="330" r="2" fill="rgba(245,158,11,0.6)"/>
    <circle cx="920" cy="330" r="2" fill="rgba(245,158,11,0.6)"/>
    <circle cx="930" cy="330" r="2" fill="rgba(245,158,11,0.6)"/>
    
    <!-- 小芯片 -->
    <rect x="200" y="600" width="30" height="30" rx="4" fill="rgba(255,255,255,0.15)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
    <circle cx="210" cy="610" r="1.5" fill="rgba(245,158,11,0.5)"/>
    <circle cx="220" cy="610" r="1.5" fill="rgba(245,158,11,0.5)"/>
    <circle cx="210" cy="620" r="1.5" fill="rgba(245,158,11,0.5)"/>
    <circle cx="220" cy="620" r="1.5" fill="rgba(245,158,11,0.5)"/>
  </g>
  
  <!-- 数据流线条 -->
  <g opacity="0.3">
    <path d="M 50 400 Q 300 200 600 400 Q 900 600 1150 400" stroke="rgba(255,255,255,0.4)" stroke-width="2" fill="none" stroke-dasharray="10,5"/>
    <path d="M 50 500 Q 400 300 800 500 Q 1000 700 1150 500" stroke="rgba(245,158,11,0.4)" stroke-width="2" fill="none" stroke-dasharray="8,4"/>
    <path d="M 50 300 Q 200 100 400 300 Q 600 500 800 300 Q 1000 100 1150 300" stroke="rgba(59,130,246,0.4)" stroke-width="2" fill="none" stroke-dasharray="6,3"/>
  </g>
  
  <!-- 浮动的几何图形 -->
  <g opacity="0.1">
    <!-- 六边形 -->
    <polygon points="800,100 830,115 830,145 800,160 770,145 770,115" fill="rgba(255,255,255,0.3)" stroke="rgba(255,255,255,0.5)" stroke-width="1"/>
    
    <!-- 三角形 -->
    <polygon points="1000,600 1020,640 980,640" fill="rgba(245,158,11,0.3)" stroke="rgba(245,158,11,0.5)" stroke-width="1"/>
    
    <!-- 圆形 -->
    <circle cx="300" cy="100" r="25" fill="rgba(59,130,246,0.2)" stroke="rgba(59,130,246,0.4)" stroke-width="1"/>
    
    <!-- 菱形 -->
    <polygon points="1100,200 1120,220 1100,240 1080,220" fill="rgba(16,185,129,0.3)" stroke="rgba(16,185,129,0.5)" stroke-width="1"/>
  </g>
  
  <!-- 发光点 -->
  <g filter="url(#glow)">
    <circle cx="150" cy="300" r="3" fill="#f59e0b" opacity="0.8">
      <animate attributeName="opacity" values="0.3;0.8;0.3" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="600" cy="150" r="2" fill="#3b82f6" opacity="0.6">
      <animate attributeName="opacity" values="0.2;0.6;0.2" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="1000" cy="500" r="2.5" fill="#10b981" opacity="0.7">
      <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="400" cy="650" r="2" fill="#8b5cf6" opacity="0.5">
      <animate attributeName="opacity" values="0.2;0.5;0.2" dur="3.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 网格线 -->
  <g opacity="0.05">
    <defs>
      <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
        <path d="M 50 0 L 0 0 0 50" fill="none" stroke="white" stroke-width="1"/>
      </pattern>
    </defs>
    <rect width="1200" height="800" fill="url(#grid)"/>
  </g>
</svg>
