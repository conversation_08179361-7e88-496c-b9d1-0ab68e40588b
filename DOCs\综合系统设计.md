# 综合系统设计

## 概述

本文档分析GD32F470VET6数据采集与存储系统的整体架构设计，包括四层架构模型、数据流设计、控制流设计、通信机制、资源管理等系统级设计内容。系统采用分层模块化架构，通过清晰的接口定义和数据流管理，实现了高可靠性、高实时性的数据采集与存储功能。

## 系统整体架构

### 四层架构模型

```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        A1[main.c<br/>系统初始化与主循环]
        A2[systick.c<br/>系统时钟管理]
        A3[gd32f4xx_it.c<br/>中断服务程序]
    end

    subgraph "功能层 (Function Layer)"
        F1[scheduler.c<br/>任务调度器]
        F2[adc_app.c<br/>ADC采样模块]
        F3[sd_app.c<br/>存储管理模块]
        F4[oled_app.c<br/>显示控制模块]
        F5[btn_app.c<br/>按键处理模块]
        F6[usart_app.c<br/>串口通信模块]
        F7[rtc_app.c<br/>时钟管理模块]
        F8[led_app.c<br/>LED指示模块]
        F9[error_handler.c<br/>错误处理模块]
    end

    subgraph "驱动层 (Driver Layer)"
        D1[BSP<br/>板级支持包]
        D2[OLED<br/>显示驱动]
        D3[GD25QXX<br/>Flash驱动]
        D4[EBTN<br/>按键库]
        D5[SDIO<br/>SD卡驱动]
        D6[FatFS<br/>文件系统]
        D7[perf_counter<br/>性能计数器]
    end

    subgraph "硬件抽象层 (HAL Layer)"
        H1[GD32F4xx HAL<br/>外设库]
        H2[CMSIS<br/>ARM标准接口]
        H3[启动文件<br/>系统启动]
    end

    A1 --> F1
    A2 --> F1
    A3 --> F1

    F1 --> F2
    F1 --> F3
    F1 --> F4
    F1 --> F5
    F1 --> F6
    F1 --> F7
    F1 --> F8

    F2 --> D1
    F3 --> D5
    F3 --> D6
    F4 --> D2
    F5 --> D4
    F6 --> D1
    F7 --> D1
    F8 --> D1
    F9 --> D1

    D1 --> H1
    D2 --> H1
    D3 --> H1
    D4 --> H1
    D5 --> H1
    D6 --> H1
    D7 --> H2

    H1 --> H2
    H2 --> H3
```

### 架构层次说明

#### 应用层 (Application Layer)
- **职责**: 系统启动、主循环控制、中断管理
- **核心组件**: main.c、systick.c、gd32f4xx_it.c
- **特点**: 系统级控制，硬件无关的应用逻辑

#### 功能层 (Function Layer)
- **职责**: 业务功能实现、任务调度、数据处理
- **核心组件**: 9个功能模块，每个模块职责单一
- **特点**: 模块化设计，接口标准化，便于维护和扩展

#### 驱动层 (Driver Layer)
- **职责**: 硬件设备驱动、第三方组件集成
- **核心组件**: BSP、外设驱动、文件系统、性能库
- **特点**: 硬件抽象，标准化接口，可移植性

#### 硬件抽象层 (HAL Layer)
- **职责**: 底层硬件访问、寄存器操作、系统启动
- **核心组件**: GD32F4xx外设库、CMSIS标准接口
- **特点**: 厂商提供，标准化，跨平台兼容

## 系统初始化序列

### 启动流程设计

```mermaid
sequenceDiagram
    participant Boot as 启动代码
    participant Main as main()
    participant BSP as BSP层
    participant Func as 功能层
    participant Sched as 调度器

    Boot->>Main: 系统复位启动
    Main->>Main: systick_config()
    Main->>Main: init_cycle_counter()
    Main->>Main: delay_ms(200)

    Note over Main,BSP: 基础硬件初始化
    Main->>BSP: bsp_led_init()
    Main->>BSP: bsp_btn_init()
    Main->>BSP: bsp_oled_init_impl()
    Main->>BSP: bsp_gd25qxx_init_impl()
    Main->>BSP: bsp_usart_init_impl()

    Note over Main,BSP: 高级硬件初始化
    Main->>BSP: bsp_adc_init_impl()
    Main->>BSP: bsp_rtc_init()

    Note over Main,Func: 功能模块初始化
    Main->>Func: error_handler_init()
    Main->>Func: sd_fatfs_init()
    Main->>Func: app_btn_init()
    Main->>Func: OLED_Init()

    Note over Main,Func: 系统配置初始化
    Main->>Func: system_config_init()
    Main->>Func: system_startup_init()
    Main->>Func: bsp_rtc_delayed_increment_power_count()

    Note over Main,Sched: 调度器启动
    Main->>Sched: scheduler_init()
    Main->>Sched: scheduler_run() (循环)
```

### 初始化阶段分析

#### 阶段1: 系统基础初始化
```c
// 系统时钟和性能计数器初始化
systick_config();           // 配置1ms系统时钟
init_cycle_counter(false);  // 初始化性能计数器
delay_ms(200);             // 等待系统稳定
```

#### 阶段2: 基础硬件初始化
```c
// 按优先级顺序初始化硬件
bsp_led_init();            // LED指示灯（最基础）
bsp_btn_init();            // 按键输入（用户交互）
bsp_oled_init_impl();      // OLED显示（状态显示）
bsp_gd25qxx_init_impl();   // SPI Flash（配置存储）
bsp_usart_init_impl();     // 串口通信（调试输出）
```

#### 阶段3: 高级硬件初始化
```c
// 数据采集和时钟管理
bsp_adc_init_impl();       // ADC采样（核心功能）
bsp_rtc_init();            // RTC时钟（时间戳）
```

#### 阶段4: 功能模块初始化
```c
// 系统服务和应用功能
error_handler_init();      // 错误处理系统
sd_fatfs_init();          // SD卡文件系统
app_btn_init();           // 按键应用层
OLED_Init();              // OLED应用层
```

#### 阶段5: 系统配置和启动
```c
// 配置管理和系统准备
system_config_init();                    // 加载系统配置
system_startup_init();                   // 系统启动初始化
bsp_rtc_delayed_increment_power_count(); // 上电次数管理
```

## 数据流设计

### 主数据流架构

```mermaid
graph LR
    subgraph "数据源"
        A1[ADC硬件]
        A2[RTC时钟]
        A3[按键输入]
        A4[串口命令]
    end

    subgraph "数据处理"
        B1[ADC采样模块]
        B2[时钟管理模块]
        B3[按键处理模块]
        B4[串口通信模块]
    end

    subgraph "数据存储"
        C1[内存缓冲区]
        C2[SD卡文件系统]
        C3[Flash存储]
    end

    subgraph "数据输出"
        D1[OLED显示]
        D2[LED指示]
        D3[串口输出]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4

    B1 --> C1
    B1 --> C2
    B1 --> C3
    B2 --> C2
    B3 --> B1
    B4 --> B1

    C1 --> D1
    B1 --> D1
    B1 --> D2
    C2 --> D3
    C3 --> D3
```

### 核心数据流分析

#### ADC数据流
```c
// ADC数据采集流程
ADC硬件采样 → DMA传输 → adc_value[1] → 电压计算 → 数据验证
    ↓
超限检测 → LED指示
    ↓
时间戳添加 → 存储处理 → SD卡/Flash
    ↓
显示更新 → OLED输出
```

#### 配置数据流
```c
// 配置参数流程
Flash存储 → 系统启动加载 → system_config全局变量
    ↓
串口命令 → 参数解析 → 参数验证 → 内存更新 → Flash保存
    ↓
功能模块 → 参数应用 → 行为调整
```

#### 状态数据流
```c
// 系统状态流程
各功能模块 → 状态更新 → 全局状态变量
    ↓
调度器轮询 → 状态检查 → 任务执行
    ↓
状态变化 → 显示更新 → 用户反馈
```

## 控制流设计

### 任务调度控制流

```mermaid
graph TD
    A[系统启动] --> B[scheduler_init]
    B --> C[scheduler_run主循环]
    C --> D[获取当前时间]
    D --> E[遍历任务数组]
    E --> F{任务到期?}
    F -->|是| G[执行任务函数]
    F -->|否| H[检查下一任务]
    G --> I[更新下次执行时间]
    I --> H
    H --> J{所有任务检查完?}
    J -->|否| F
    J -->|是| K[统计调度次数]
    K --> C
```

### 用户交互控制流

```mermaid
graph TD
    A[按键按下] --> B[btn_task检测]
    B --> C[防抖处理]
    C --> D{按键类型}
    D -->|KEY1| E[采样控制]
    D -->|KEY2-6| F[功能扩展]
    D -->|KEYW| G[特殊功能]

    E --> H{当前状态}
    H -->|停止| I[启动采样]
    H -->|运行| J[停止采样]

    I --> K[更新采样状态]
    J --> K
    K --> L[LED状态更新]
    L --> M[OLED显示更新]

    F --> N[功能待扩展]
    G --> O[特殊功能处理]
```

### 错误处理控制流

```mermaid
graph TD
    A[错误检测] --> B[report_error]
    B --> C[错误分级]
    C --> D{错误级别}

    D -->|INFO| E[记录日志]
    D -->|WARNING| F[记录日志+警告输出]
    D -->|ERROR| G[记录日志+尝试恢复]
    D -->|CRITICAL| H[记录日志+系统保护]

    G --> I{恢复成功?}
    I -->|是| J[继续运行]
    I -->|否| K[降级运行]

    H --> L[安全模式]

    E --> M[正常运行]
    F --> M
    J --> M
    K --> N[受限功能]
    L --> O[最小功能]
```

## 通信机制设计

### 模块间通信架构

#### 全局数据共享
```c
// 核心全局变量
extern config_params_t system_config;    // 系统配置参数
extern sampling_state_t sampling_state;  // 采样状态
extern storage_state_t storage_state;    // 存储状态
extern uint16_t adc_value[1];            // ADC数据缓冲区
extern uint8_t led_state_array[6];       // LED状态数组
```

#### 函数接口调用
```c
// 标准化接口设计
// 初始化接口: xxx_init()
// 任务接口: xxx_task()
// 状态查询: xxx_get_xxx()
// 配置设置: xxx_set_xxx()
// 统计接口: xxx_get_count()
```

#### 事件通知机制
```c
// 电压更新通知
void oled_update_voltage(float voltage, uint32_t timestamp);

// 超限状态通知
void led2_overlimit_set(uint8_t state);

// 错误报告通知
void report_error(system_error_t error_code, error_level_t level,
                 const char* module, const char* description);
```

### 硬件通信接口

#### DMA通信机制
```c
// USART DMA接收配置
DMA1_CH2: USART0_RX → rxbuffer[512]
- 优先级: ULTRA_HIGH
- 模式: 外设到内存
- 触发: 空闲中断

// ADC DMA传输配置
DMA1_CH0: ADC0_RDATA → adc_value[1]
- 优先级: HIGH
- 模式: 循环传输
- 触发: ADC转换完成

// I2C DMA传输配置
DMA0_CH6: 内存 → I2C0_DATA
- 优先级: ULTRA_HIGH
- 模式: 内存到外设
- 用途: OLED显示数据传输
```

#### 中断优先级配置
```c
// 中断优先级分配（数值越小优先级越高）
USART0_IRQn     = 0,  // 串口空闲中断（最高优先级）
DMA1_Channel2   = 1,  // 串口DMA接收中断
DMA1_Channel0   = 2,  // ADC DMA传输中断
SysTick_IRQn    = 3,  // 系统时钟中断
```

## 资源管理设计

### 内存资源管理

#### Flash存储分配
```c
// Flash存储区域规划（512KB总容量）
0x08000000 - 0x08005FFF  // 代码区（24KB）
0x08001000 - 0x08001FFF  // 配置参数区（4KB）
0x08002000 - 0x08002FFF  // 设备ID区（4KB）
0x08003000 - 0x08003FFF  // 日志缓存区（4KB）
0x08004000 - 0x08004FFF  // 备份数据区（4KB）
0x08005000 - 0x0807FFFF  // 预留扩展区（476KB）
```

#### RAM内存分配
```c
// RAM内存区域规划（192KB总容量）
0x20000000 - 0x200003FF  // 系统栈（1KB）
0x20000400 - 0x200005FF  // 系统堆（512B）
0x20000600 - 0x20000BFF  // 全局变量区（1.5KB）
0x20000C00 - 0x200013FF  // 缓冲区域（2KB）
0x20001400 - 0x2002FFFF  // 可用内存（187KB）
```

#### 缓冲区管理
```c
// 专用缓冲区分配
uint8_t rxbuffer[512];              // 串口接收缓冲区
uint16_t adc_value[1];              // ADC数据缓冲区
uint8_t spi1_send_array[256];       // SPI发送缓冲区
uint8_t spi1_receive_array[256];    // SPI接收缓冲区
uint8_t oled_cmd_buf[2];            // OLED命令缓冲区
uint8_t oled_data_buf[2];           // OLED数据缓冲区
```

### 外设资源管理

#### GPIO资源分配
```c
// LED输出引脚
LED1-LED6: PE2-PE7 (输出模式，推挽输出)

// 按键输入引脚
KEY1-KEY6: PA0-PA5 (输入模式，上拉输入)
KEYW: PC13 (输入模式，上拉输入)

// ADC采样引脚
ADC_CH10: PC0 (模拟输入模式)

// 串口通信引脚
USART0_TX: PA9 (复用功能，AF7)
USART0_RX: PA10 (复用功能，AF7)

// I2C通信引脚
I2C0_SCL: PB8 (复用功能，AF4，开漏输出)
I2C0_SDA: PB9 (复用功能，AF4，开漏输出)

// SPI通信引脚
SPI1_SCK: PB13 (复用功能，AF5)
SPI1_MISO: PB14 (复用功能，AF5)
SPI1_MOSI: PB15 (复用功能，AF5)
SPI1_NSS: PB12 (GPIO输出模式)
```

#### 定时器资源分配
```c
// 系统时钟定时器
SysTick: 1ms系统时钟，用于任务调度和延时

// ADC触发定时器
TIMER5: ADC采样触发源（可选）
- 预分频: 239 (240MHz/240 = 1MHz)
- 周期: 99 (1MHz/100 = 10kHz)
- 用途: 定时ADC采样触发

// 性能计数器
DWT_CYCCNT: CPU周期计数器，用于精确时间测量
```

#### DMA通道分配
```c
// DMA1通道分配
DMA1_CH0: ADC0数据传输 (ADC → 内存)
DMA1_CH2: USART0接收 (外设 → 内存)

// DMA0通道分配
DMA0_CH6: I2C0发送 (内存 → 外设)
DMA0_CH1: SPI1发送 (内存 → 外设，预留)
DMA0_CH0: SPI1接收 (外设 → 内存，预留)
```

## 系统配置管理

### 配置层次结构

#### 编译时配置 (my_config.h)
```c
// 硬件配置常量
#define ADC_CHANNEL_COUNT       1
#define LED_COUNT               6
#define BUTTON_COUNT            7
#define BUFFER_SIZE_MAX         512

// 任务调度配置
#define TASK_PERIOD_ADC_MS      100
#define TASK_PERIOD_LED_MS      50
#define TASK_PERIOD_OLED_MS     100
#define TASK_PERIOD_BTN_MS      5
#define TASK_PERIOD_UART_MS     5
#define TASK_PERIOD_RTC_MS      500

// Flash地址配置
#define FLASH_CONFIG_ADDR       0x1000
#define FLASH_DEVICE_ID_ADDR    0x2000
#define FLASH_LOG_CACHE_ADDR    0x3000
```

#### 运行时配置 (Flash存储)
```c
typedef struct {
    float ratio_ch0;        // ADC通道0变比系数
    float limit_ch0;        // ADC通道0阈值限制
    uint32_t sample_period; // 采样周期（秒）
    uint32_t crc32;         // 配置校验码
} config_params_t;
```

#### 动态配置 (串口命令)
```c
// 支持的配置命令格式
"set ratio <value>"     // 设置ADC变比系数
"set limit <value>"     // 设置阈值限制
"set period <value>"    // 设置采样周期
"set device_id <id>"    // 设置设备ID
```

### 配置同步机制

#### 配置加载流程
```c
void system_config_init(void)
{
    config_params_t temp_config;

    // 1. 尝试从Flash读取配置
    if(read_config_from_flash(&temp_config) == SUCCESS) {
        // 2. 读取成功，更新系统配置
        system_config = temp_config;
        sampling_state.sample_period = system_config.sample_period;
    } else {
        // 3. 读取失败，使用默认值
        system_config.ratio_ch0 = 1.0f;
        system_config.limit_ch0 = 1.0f;
        system_config.sample_period = 5;

        // 4. 保存默认配置到Flash
        write_config_to_flash(&system_config);
    }
}
```

#### 配置更新流程
```c
// 串口命令 → 参数解析 → 参数验证 → 内存更新 → Flash保存 → 确认反馈
void config_set_handler(char* param, char* value)
{
    // 1. 参数验证
    if (validate_parameter(param, value)) {
        // 2. 更新内存配置
        update_system_config(param, value);

        // 3. 保存到Flash
        write_config_to_flash(&system_config);

        // 4. 确认反馈
        my_printf(DEBUG_USART, "Config updated: %s = %s\r\n", param, value);
    } else {
        my_printf(DEBUG_USART, "Invalid parameter\r\n");
    }
}
```

## 总结

本系统采用了清晰的四层架构设计，通过标准化的接口定义、高效的数据流管理、灵活的控制流设计和完善的资源管理，实现了一个高可靠性、高实时性的数据采集与存储系统。

### 设计优势

1. **分层架构**: 清晰的层次划分，便于理解和维护
2. **模块化设计**: 功能模块独立，接口标准化
3. **数据流优化**: 高效的数据传输和处理机制
4. **资源管理**: 合理的内存和外设资源分配
5. **配置灵活**: 多层次配置管理，支持运行时调整
6. **错误处理**: 完善的错误检测和恢复机制

### 技术特色

1. **双重存储**: SD卡主存储+Flash备份，确保数据安全
2. **DMA传输**: 高效的数据传输，减少CPU负载
3. **实时调度**: 时间片轮询调度，满足实时性要求
4. **状态同步**: 全局状态管理，确保系统一致性
5. **性能监控**: 集成性能计数器，支持系统优化

该系统设计为嵌入式数据采集系统提供了一个完整、可靠、高效的架构方案，具有很好的工程实用价值和技术参考意义。